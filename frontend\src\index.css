@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Custom Properties - Default values (Corporate theme) */
:root {
  /* Colors */
  --color-primary: #1e40af;
  --color-secondary: #64748b;
  --color-accent: #3b82f6;
  --color-background: #ffffff;
  --color-surface: #f8fafc;
  --color-overlay: #ffffff;
  
  /* Text colors */
  --color-text-primary: #1e293b;
  --color-text-secondary: #64748b;
  --color-text-accent: #1e40af;
  --color-text-inverse: #ffffff;
  
  /* Status colors */
  --color-success: #059669;
  --color-warning: #d97706;
  --color-error: #dc2626;
  --color-info: #0284c7;
  
  /* Border and divider colors */
  --color-border: #e2e8f0;
  --color-divider: #f1f5f9;
  
  /* Interactive state colors */
  --color-hover: #f1f5f9;
  --color-active: #e2e8f0;
  --color-focus: #3b82f6;
  --color-disabled: #9ca3af;
  
  /* Typography */
  --font-family-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  --font-family-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-family-mono: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  --font-family-display: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  
  /* Font sizes */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  
  /* Spacing */
  --border-radius-none: 0;
  --border-radius-sm: 0.125rem;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 0.75rem;
  --border-radius-full: 9999px;
  
  --spacing-xs: 0.5rem;
  --spacing-sm: 0.75rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
  --shadow-glow: 0 0 20px rgb(59 130 246 / 0.5);
  
  /* Glassmorphism */
  --glass-blur: 10px;
  --glass-opacity: 0.95;
  --glass-border: 1px solid rgba(255, 255, 255, 0.2);
  
  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  --easing-ease: ease;
  --easing-ease-in: ease-in;
  --easing-ease-out: ease-out;
  --easing-ease-in-out: ease-in-out;
}

@layer base {
  body {
    background-color: var(--color-background);
    color: var(--color-text-primary);
    font-family: var(--font-family-sans);
    transition: background-color var(--duration-normal) var(--easing-ease),
                color var(--duration-normal) var(--easing-ease);
  }
  
  /* Scrollbar styling for themed environments */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: var(--color-surface);
  }
  
  ::-webkit-scrollbar-thumb {
    background: var(--color-border);
    border-radius: var(--border-radius-md);
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: var(--color-primary);
  }
}

@layer components {
  .card {
    background-color: var(--color-surface);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--color-border);
    padding: var(--spacing-lg);
    transition: all var(--duration-normal) var(--easing-ease);
  }
  
  .card:hover {
    background-color: var(--color-hover);
    box-shadow: var(--shadow-md);
  }
  
  .card-glass {
    backdrop-filter: blur(var(--glass-blur));
    background-color: color-mix(in srgb, var(--color-surface) calc(var(--glass-opacity) * 100%), transparent);
    border: var(--glass-border);
  }
  
  .btn-primary {
    background-color: var(--color-primary);
    color: var(--color-text-inverse);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
    font-weight: 500;
    transition: all var(--duration-fast) var(--easing-ease);
    border: none;
    cursor: pointer;
  }
  
  .btn-primary:hover {
    background-color: color-mix(in srgb, var(--color-primary) 80%, black);
    box-shadow: var(--shadow-md);
  }
  
  .btn-primary:focus {
    outline: 2px solid var(--color-focus);
    outline-offset: 2px;
  }
  
  /* Multicolored gradient variant for primary buttons */
  .btn-gradient {
    background-image: linear-gradient(135deg, #ff6b6b 0%, #f7b42c 30%, #3ec5ff 65%, #845ec2 100%);
    background-size: 200% 200%;
    color: var(--color-text-inverse);
    border: 1px solid rgba(255, 255, 255, 0.3);
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.35), 0 0 8px rgba(0, 0, 0, 0.2);
    transition: background-position var(--duration-normal) var(--easing-ease),
                box-shadow var(--duration-fast) var(--easing-ease),
                transform var(--duration-fast) var(--easing-ease);
  }
  
  .btn-gradient:hover {
    background-position: 100% 0;
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
  }
  
  .btn-gradient:active {
    transform: translateY(0);
  }
  
  .btn-gradient:disabled {
    filter: saturate(0.75) brightness(0.95);
    cursor: not-allowed;
  }
  
  .btn-secondary {
    background-color: var(--color-surface);
    color: var(--color-text-primary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
    font-weight: 500;
    transition: all var(--duration-fast) var(--easing-ease);
    border: 1px solid var(--color-border);
    cursor: pointer;
  }
  
  .btn-secondary:hover {
    background-color: var(--color-hover);
    border-color: var(--color-primary);
  }
  
  .btn-secondary:focus {
    outline: 2px solid var(--color-focus);
    outline-offset: 2px;
  }

  /* Form elements */
  .input {
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--color-text-primary);
    font-size: var(--font-size-base);
    transition: all var(--duration-fast) var(--easing-ease);
  }
  
  .input:focus {
    outline: none;
    border-color: var(--color-focus);
    box-shadow: 0 0 0 3px color-mix(in srgb, var(--color-focus) 20%, transparent);
  }
  
  .input::placeholder {
    color: var(--color-text-secondary);
  }

  /* Custom slider styles for relevance filter */
  .slider {
    appearance: none;
    width: 100%;
    height: 6px;
    background: var(--color-border);
    border-radius: var(--border-radius-full);
    outline: none;
    transition: background var(--duration-normal) var(--easing-ease);
  }
  
  .slider::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    background: var(--color-primary);
    border-radius: var(--border-radius-full);
    cursor: pointer;
    transition: all var(--duration-fast) var(--easing-ease);
  }
  
  .slider::-webkit-slider-thumb:hover {
    box-shadow: var(--shadow-glow);
    transform: scale(1.1);
  }
  
  .slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: var(--color-primary);
    border-radius: var(--border-radius-full);
    cursor: pointer;
    border: none;
    transition: all var(--duration-fast) var(--easing-ease);
  }
  
  .slider::-moz-range-thumb:hover {
    box-shadow: var(--shadow-glow);
    transform: scale(1.1);
  }

  /* Filter chip animations */
  .filter-chip {
    background-color: var(--color-accent);
    color: var(--color-text-inverse);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-sm);
    transition: all var(--duration-normal) var(--easing-ease-in-out);
    border: none;
    cursor: pointer;
  }
  
  .filter-chip:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
  }
  
  .filter-chip:active {
    transform: scale(0.95);
  }
  
  /* Status indicators */
  .status-success {
    color: var(--color-success);
  }
  
  .status-warning {
    color: var(--color-warning);
  }
  
  .status-error {
    color: var(--color-error);
  }
  
  .status-info {
    color: var(--color-info);
  }
  
  /* Loading animations */
  .loading-pulse {
    animation: pulse var(--duration-slow) cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
  
  .loading-spin {
    animation: spin calc(var(--duration-slow) * 2) linear infinite;
  }
  
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}

/* Theme-specific styles */
@layer utilities {
  /* Glassmorphism utilities */
  .glass-light {
    backdrop-filter: blur(var(--glass-blur));
    background-color: color-mix(in srgb, var(--color-surface) 80%, transparent);
    border: var(--glass-border);
  }
  
  .glass-medium {
    backdrop-filter: blur(var(--glass-blur));
    background-color: color-mix(in srgb, var(--color-surface) 60%, transparent);
    border: var(--glass-border);
  }
  
  .glass-heavy {
    backdrop-filter: blur(var(--glass-blur));
    background-color: color-mix(in srgb, var(--color-surface) 40%, transparent);
    border: var(--glass-border);
  }
  
  /* Glow effects for neon themes */
  .glow-primary {
    box-shadow: 0 0 calc(var(--glow-intensity, 20) * 1px) var(--color-primary);
  }
  
  .glow-accent {
    box-shadow: 0 0 calc(var(--glow-intensity, 20) * 1px) var(--color-accent);
  }
  
  .text-glow {
    text-shadow: 0 0 calc(var(--glow-intensity, 10) * 1px) currentColor;
  }
  
  /* Animation utilities */
  .animate-float {
    animation: float calc(var(--duration-slow) * 6) ease-in-out infinite;
  }
  
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }
  
  .animate-pulse-glow {
    animation: pulse-glow calc(var(--pulse-rate, 2) * 1s) ease-in-out infinite;
  }
  
  @keyframes pulse-glow {
    0%, 100% {
      box-shadow: 0 0 5px var(--color-primary);
    }
    50% {
      box-shadow: 0 0 calc(var(--glow-intensity, 20) * 1px) var(--color-primary),
                  0 0 calc(var(--glow-intensity, 30) * 1px) var(--color-primary);
    }
  }
}

/* Theme-specific feature styles */

/* Cyberpunk theme effects */
.theme-cyberpunk {
  /* Matrix-like data streams */
  .data-streams::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    background-image: 
      repeating-linear-gradient(
        90deg,
        transparent,
        transparent 98px,
        var(--color-primary) 100px
      );
    opacity: 0.1;
    animation: data-stream calc(var(--data-stream-count, 10) * 1s) linear infinite;
  }
  
  @keyframes data-stream {
    0% {
      transform: translateX(-100px);
    }
    100% {
      transform: translateX(100px);
    }
  }
}

/* Forest theme effects */
.theme-forest {
  /* Organic wave animations */
  .organic-wave {
    position: relative;
    overflow: hidden;
  }
  
  .organic-wave::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
      45deg,
      transparent 40%,
      color-mix(in srgb, var(--color-primary) 10%, transparent) 50%,
      transparent 60%
    );
    animation: organic-flow calc(var(--wave-speed, 1) * 20s) ease-in-out infinite;
  }
  
  @keyframes organic-flow {
    0%, 100% {
      transform: rotate(0deg);
    }
    50% {
      transform: rotate(180deg);
    }
  }
}

/* Ocean theme effects */
.theme-ocean {
  /* Wave animations */
  .ocean-waves::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100px;
    background: linear-gradient(
      to top,
      color-mix(in srgb, var(--color-primary) 20%, transparent),
      transparent
    );
    animation: wave calc(var(--wave-speed, 1) * 10s) ease-in-out infinite;
  }
  
  @keyframes wave {
    0%, 100% {
      transform: translateX(0) scaleY(1);
    }
    50% {
      transform: translateX(-50px) scaleY(1.2);
    }
  }
}

/* Winter theme effects */
.theme-winter {
  /* Snowflake animation container */
  .snowflakes {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
  }
  
  .snowflake {
    position: absolute;
    color: var(--color-border);
    user-select: none;
    pointer-events: none;
    animation: snowfall linear infinite;
  }
  
  @keyframes snowfall {
    0% {
      transform: translateY(-100px) rotate(0deg);
      opacity: 1;
    }
    100% {
      transform: translateY(100vh) rotate(360deg);
      opacity: 0;
    }
  }
}

/* High contrast theme overrides */
.theme-high-contrast {
  /* Ensure maximum contrast */
  .card,
  .btn-secondary,
  .input {
    border-width: 2px;
    border-color: var(--color-border);
  }
  
  /* Remove transparency effects */
  .glass-light,
  .glass-medium,
  .glass-heavy {
    backdrop-filter: none;
    background-color: var(--color-surface);
  }
  
  /* Stronger focus indicators */
  .btn-primary:focus,
  .btn-secondary:focus,
  .input:focus {
    outline-width: 4px;
    outline-offset: 4px;
  }
}

/* Large text theme overrides */
.theme-large-text {
  /* Increase minimum click targets */
  .btn-primary,
  .btn-secondary {
    min-height: 44px;
    min-width: 44px;
    padding: var(--spacing-md) var(--spacing-lg);
  }
  
  .filter-chip {
    min-height: 32px;
    padding: var(--spacing-sm) var(--spacing-md);
  }
  
  /* Increase spacing for better readability */
  .card {
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
  }
}

/* Accessibility preference overrides */

/* Reduced motion preference */
.reduce-motion,
.reduce-motion * {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

.reduce-motion .loading-pulse,
.reduce-motion .loading-spin,
.reduce-motion .animate-float,
.reduce-motion .animate-pulse-glow {
  animation: none !important;
}

/* Force high contrast */
.force-high-contrast {
  /* Override theme colors for maximum contrast */
  --color-background: #ffffff !important;
  --color-surface: #f8f8f8 !important;
  --color-text-primary: #000000 !important;
  --color-text-secondary: #000000 !important;
  --color-border: #000000 !important;
  --color-primary: #0000ff !important;
  --color-success: #008000 !important;
  --color-warning: #ff8000 !important;
  --color-error: #ff0000 !important;
  --color-focus: #ff0000 !important;
}

.force-high-contrast .card,
.force-high-contrast .btn-secondary,
.force-high-contrast .input {
  border-width: 2px !important;
  border-color: var(--color-border) !important;
}

.force-high-contrast .glass-light,
.force-high-contrast .glass-medium,
.force-high-contrast .glass-heavy {
  backdrop-filter: none !important;
  background-color: var(--color-surface) !important;
}

/* Force large text */
.force-large-text {
  --font-size-xs: 1rem !important;
  --font-size-sm: 1.125rem !important;
  --font-size-base: 1.25rem !important;
  --font-size-lg: 1.5rem !important;
  --font-size-xl: 1.75rem !important;
  --font-size-2xl: 2rem !important;
  --font-size-3xl: 2.5rem !important;
  --font-size-4xl: 3rem !important;
  
  --line-height-tight: 1.4 !important;
  --line-height-normal: 1.6 !important;
  --line-height-relaxed: 1.8 !important;
  
  --spacing-xs: 0.75rem !important;
  --spacing-sm: 1rem !important;
  --spacing-md: 1.5rem !important;
  --spacing-lg: 2rem !important;
  --spacing-xl: 3rem !important;
  --spacing-2xl: 4rem !important;
  --spacing-3xl: 5rem !important;
}

.force-large-text .btn-primary,
.force-large-text .btn-secondary {
  min-height: 44px !important;
  min-width: 44px !important;
  padding: var(--spacing-md) var(--spacing-lg) !important;
}

.force-large-text .filter-chip {
  min-height: 32px !important;
  padding: var(--spacing-sm) var(--spacing-md) !important;
}

.force-large-text .card {
  padding: var(--spacing-xl) !important;
  margin-bottom: var(--spacing-lg) !important;
}

/* Focus improvements for accessibility */
@media (prefers-reduced-motion: no-preference) {
  .btn-primary:focus,
  .btn-secondary:focus,
  .input:focus,
  .filter-chip:focus {
    transition: outline var(--duration-fast) var(--easing-ease);
  }
}

/* High contrast media query support */
@media (prefers-contrast: high) {
  :root {
    --color-border: #000000;
    --shadow-sm: 0 2px 4px 0 rgb(0 0 0 / 0.3);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.3);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.5), 0 4px 6px -4px rgb(0 0 0 / 0.3);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.6), 0 8px 10px -6px rgb(0 0 0 / 0.3);
  }
  
  .card,
  .btn-secondary,
  .input {
    border-width: 2px;
  }
}

/* Reduced motion media query support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .loading-pulse,
  .loading-spin,
  .animate-float,
  .animate-pulse-glow {
    animation: none !important;
  }
}