<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Obby - Note Monitor (Solarized Dark)</title>
    <style>
        /* Solarized Dark Color Palette */
        :root {
            --base03: #002b36;    /* background */
            --base02: #073642;    /* highlights */
            --base01: #586e75;    /* secondary content */
            --base00: #657b83;    /* body text */
            --base0: #839496;     /* primary text */
            --base1: #93a1a1;     /* emphasized text */
            --base2: #eee8d5;     /* background highlights */
            --base3: #fdf6e3;     /* background highlights */
            --yellow: #b58900;
            --orange: #cb4b16;
            --red: #dc322f;
            --magenta: #d33682;
            --violet: #6c71c4;
            --blue: #268bd2;
            --cyan: #2aa198;
            --green: #859900;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            background-color: var(--base03);
            color: var(--base0);
            line-height: 1.6;
            overflow-x: hidden;
        }

        .app-container {
            display: flex;
            height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            width: 280px;
            background-color: var(--base02);
            border-right: 1px solid var(--base01);
            padding: 20px;
            overflow-y: auto;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: var(--blue);
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo::before {
            content: "◉";
            color: var(--cyan);
        }

        .nav-section {
            margin-bottom: 25px;
        }

        .nav-title {
            font-size: 12px;
            text-transform: uppercase;
            color: var(--base01);
            margin-bottom: 10px;
            letter-spacing: 1px;
        }

        .nav-item {
            padding: 8px 12px;
            margin: 2px 0;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-item:hover {
            background-color: var(--base01);
        }

        .nav-item.active {
            background-color: var(--blue);
            color: var(--base03);
        }

        .nav-item::before {
            width: 4px;
            text-align: center;
        }

        .nav-item[data-icon="dashboard"]::before { content: "⊡"; }
        .nav-item[data-icon="files"]::before { content: "📁"; }
        .nav-item[data-icon="search"]::before { content: "🔍"; }
        .nav-item[data-icon="diff"]::before { content: "⟷"; }
        .nav-item[data-icon="settings"]::before { content: "⚙"; }

        /* Stats */
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 20px;
        }

        .stat-item {
            background-color: var(--base03);
            padding: 12px;
            border-radius: 6px;
            border: 1px solid var(--base01);
        }

        .stat-value {
            font-size: 20px;
            font-weight: bold;
            color: var(--cyan);
        }

        .stat-label {
            font-size: 11px;
            color: var(--base01);
            text-transform: uppercase;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* Header */
        .header {
            background-color: var(--base02);
            padding: 15px 25px;
            border-bottom: 1px solid var(--base01);
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .page-title {
            font-size: 18px;
            color: var(--base1);
        }

        .breadcrumb {
            color: var(--base01);
            font-size: 14px;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-left: auto;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            border-radius: 4px;
            background-color: var(--base03);
            font-size: 12px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: var(--green);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Content Area */
        .content-area {
            flex: 1;
            padding: 25px;
            overflow-y: auto;
        }

        /* File List */
        .file-list {
            background-color: var(--base02);
            border-radius: 8px;
            border: 1px solid var(--base01);
            overflow: hidden;
        }

        .file-header {
            padding: 15px 20px;
            background-color: var(--base01);
            border-bottom: 1px solid var(--base00);
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .file-header h3 {
            color: var(--base1);
            font-size: 14px;
        }

        .file-count {
            color: var(--base01);
            font-size: 12px;
        }

        .file-item {
            padding: 15px 20px;
            border-bottom: 1px solid var(--base01);
            transition: background-color 0.2s;
        }

        .file-item:hover {
            background-color: var(--base01);
        }

        .file-item:last-child {
            border-bottom: none;
        }

        .file-path {
            font-family: 'Monaco', 'Menlo', monospace;
            color: var(--blue);
            font-size: 13px;
            margin-bottom: 5px;
        }

        .file-meta {
            display: flex;
            gap: 15px;
            font-size: 11px;
            color: var(--base01);
        }

        .change-type {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            text-transform: uppercase;
            font-weight: bold;
        }

        .change-type.modified {
            background-color: var(--yellow);
            color: var(--base03);
        }

        .change-type.added {
            background-color: var(--green);
            color: var(--base03);
        }

        .change-type.deleted {
            background-color: var(--red);
            color: var(--base03);
        }

        /* Diff Viewer */
        .diff-container {
            background-color: var(--base02);
            border-radius: 8px;
            border: 1px solid var(--base01);
            margin-top: 20px;
            overflow: hidden;
        }

        .diff-header {
            padding: 12px 20px;
            background-color: var(--base01);
            border-bottom: 1px solid var(--base00);
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .diff-file-path {
            font-family: 'Monaco', 'Menlo', monospace;
            color: var(--cyan);
            font-size: 13px;
        }

        .diff-stats {
            color: var(--base01);
            font-size: 12px;
        }

        .diff-content {
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            line-height: 1.4;
        }

        .diff-line {
            padding: 2px 20px;
            display: flex;
            white-space: pre;
        }

        .diff-line-number {
            width: 60px;
            color: var(--base01);
            text-align: right;
            padding-right: 15px;
            user-select: none;
            flex-shrink: 0;
        }

        .diff-line-content {
            flex: 1;
        }

        .diff-line.added {
            background-color: rgba(133, 153, 0, 0.1);
            border-left: 3px solid var(--green);
        }

        .diff-line.removed {
            background-color: rgba(220, 50, 47, 0.1);
            border-left: 3px solid var(--red);
        }

        .diff-line.context {
            color: var(--base00);
        }

        .diff-line.added .diff-line-content {
            color: var(--green);
        }

        .diff-line.removed .diff-line-content {
            color: var(--red);
        }

        /* Syntax Highlighting */
        .keyword { color: var(--green); }
        .string { color: var(--cyan); }
        .comment { color: var(--base01); font-style: italic; }
        .function { color: var(--blue); }
        .variable { color: var(--base0); }
        .number { color: var(--violet); }
        .operator { color: var(--orange); }

        /* Search Bar */
        .search-container {
            margin-bottom: 20px;
        }

        .search-input {
            width: 100%;
            padding: 12px 16px;
            background-color: var(--base02);
            border: 1px solid var(--base01);
            border-radius: 6px;
            color: var(--base0);
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--blue);
            box-shadow: 0 0 0 2px rgba(38, 139, 210, 0.2);
        }

        .search-input::placeholder {
            color: var(--base01);
        }

        /* Tags */
        .tags {
            display: flex;
            gap: 6px;
            margin-top: 8px;
            flex-wrap: wrap;
        }

        .tag {
            padding: 2px 8px;
            background-color: var(--base01);
            color: var(--base1);
            border-radius: 12px;
            font-size: 10px;
            text-transform: lowercase;
        }

        .tag.ai { background-color: var(--magenta); color: var(--base03); }
        .tag.config { background-color: var(--orange); color: var(--base03); }
        .tag.docs { background-color: var(--blue); color: var(--base03); }

        /* Footer */
        .footer {
            padding: 10px 20px;
            background-color: var(--base02);
            border-top: 1px solid var(--base01);
            font-size: 11px;
            color: var(--base01);
            text-align: center;
        }

        /* Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--base03);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--base01);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--base00);
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="logo">Obby</div>
            
            <nav>
                <div class="nav-section">
                    <div class="nav-title">Monitor</div>
                    <div class="nav-item active" data-icon="dashboard">Dashboard</div>
                    <div class="nav-item" data-icon="files">Recent Files</div>
                    <div class="nav-item" data-icon="search">Search</div>
                    <div class="nav-item" data-icon="diff">Diff Viewer</div>
                </div>
                
                <div class="nav-section">
                    <div class="nav-title">Tools</div>
                    <div class="nav-item" data-icon="settings">Settings</div>
                </div>
            </nav>

            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value">247</div>
                    <div class="stat-label">Files Watched</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">1.2k</div>
                    <div class="stat-label">Changes</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">15</div>
                    <div class="stat-label">Today</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">3.4MB</div>
                    <div class="stat-label">Indexed</div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-left">
                    <h1 class="page-title">File Monitor Dashboard</h1>
                    <span class="breadcrumb">/ monitoring / active</span>
                </div>
                <div class="header-right">
                    <div class="status-indicator">
                        <div class="status-dot"></div>
                        <span>Watching</span>
                    </div>
                </div>
            </header>

            <!-- Content Area -->
            <div class="content-area">
                <!-- Search -->
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search files, content, or use topic:name syntax...">
                </div>

                <!-- Recent Files -->
                <div class="file-list">
                    <div class="file-header">
                        <h3>Recent Changes</h3>
                        <span class="file-count">Last 24 hours • 15 files</span>
                    </div>
                    
                    <div class="file-item">
                        <div class="file-path">src/components/Dashboard.tsx</div>
                        <div class="file-meta">
                            <span class="change-type modified">Modified</span>
                            <span>2 minutes ago</span>
                            <span>+12 -5 lines</span>
                        </div>
                        <div class="tags">
                            <span class="tag">react</span>
                            <span class="tag">typescript</span>
                            <span class="tag ai">ai-analyzed</span>
                        </div>
                    </div>

                    <div class="file-item">
                        <div class="file-path">config/database.json</div>
                        <div class="file-meta">
                            <span class="change-type modified">Modified</span>
                            <span>8 minutes ago</span>
                            <span>+3 -1 lines</span>
                        </div>
                        <div class="tags">
                            <span class="tag config">config</span>
                            <span class="tag">json</span>
                        </div>
                    </div>

                    <div class="file-item">
                        <div class="file-path">docs/api-reference.md</div>
                        <div class="file-meta">
                            <span class="change-type added">Added</span>
                            <span>15 minutes ago</span>
                            <span>+45 lines</span>
                        </div>
                        <div class="tags">
                            <span class="tag docs">documentation</span>
                            <span class="tag">markdown</span>
                        </div>
                    </div>

                    <div class="file-item">
                        <div class="file-path">src/utils/fileWatcher.js</div>
                        <div class="file-meta">
                            <span class="change-type deleted">Deleted</span>
                            <span>32 minutes ago</span>
                            <span>-67 lines</span>
                        </div>
                        <div class="tags">
                            <span class="tag">javascript</span>
                            <span class="tag">utilities</span>
                        </div>
                    </div>
                </div>

                <!-- Diff Viewer -->
                <div class="diff-container">
                    <div class="diff-header">
                        <div class="diff-file-path">src/components/Dashboard.tsx</div>
                        <div class="diff-stats">+12 -5 lines</div>
                    </div>
                    
                    <div class="diff-content">
                        <div class="diff-line context">
                            <div class="diff-line-number">42</div>
                            <div class="diff-line-content">  <span class="keyword">const</span> <span class="variable">Dashboard</span> = () => {</div>
                        </div>
                        
                        <div class="diff-line context">
                            <div class="diff-line-number">43</div>
                            <div class="diff-line-content">    <span class="keyword">const</span> [<span class="variable">files</span>, <span class="variable">setFiles</span>] = <span class="function">useState</span>([]);</div>
                        </div>
                        
                        <div class="diff-line removed">
                            <div class="diff-line-number">-</div>
                            <div class="diff-line-content">    <span class="keyword">const</span> [<span class="variable">loading</span>, <span class="variable">setLoading</span>] = <span class="function">useState</span>(<span class="keyword">false</span>);</div>
                        </div>
                        
                        <div class="diff-line added">
                            <div class="diff-line-number">+</div>
                            <div class="diff-line-content">    <span class="keyword">const</span> [<span class="variable">loading</span>, <span class="variable">setLoading</span>] = <span class="function">useState</span>(<span class="keyword">true</span>);</div>
                        </div>
                        
                        <div class="diff-line added">
                            <div class="diff-line-number">+</div>
                            <div class="diff-line-content">    <span class="keyword">const</span> [<span class="variable">error</span>, <span class="variable">setError</span>] = <span class="function">useState</span>(<span class="keyword">null</span>);</div>
                        </div>
                        
                        <div class="diff-line context">
                            <div class="diff-line-number">46</div>
                            <div class="diff-line-content"></div>
                        </div>
                        
                        <div class="diff-line context">
                            <div class="diff-line-number">47</div>
                            <div class="diff-line-content">    <span class="function">useEffect</span>(() => {</div>
                        </div>
                        
                        <div class="diff-line added">
                            <div class="diff-line-number">+</div>
                            <div class="diff-line-content">      <span class="function">fetchRecentFiles</span>()</div>
                        </div>
                        
                        <div class="diff-line added">
                            <div class="diff-line-number">+</div>
                            <div class="diff-line-content">        .<span class="function">then</span>(<span class="variable">data</span> => {</div>
                        </div>
                        
                        <div class="diff-line added">
                            <div class="diff-line-number">+</div>
                            <div class="diff-line-content">          <span class="function">setFiles</span>(<span class="variable">data</span>);</div>
                        </div>
                        
                        <div class="diff-line added">
                            <div class="diff-line-number">+</div>
                            <div class="diff-line-content">          <span class="function">setLoading</span>(<span class="keyword">false</span>);</div>
                        </div>
                        
                        <div class="diff-line added">
                            <div class="diff-line-number">+</div>
                            <div class="diff-line-content">        })</div>
                        </div>
                        
                        <div class="diff-line added">
                            <div class="diff-line-number">+</div>
                            <div class="diff-line-content">        .<span class="function">catch</span>(<span class="variable">err</span> => {</div>
                        </div>
                        
                        <div class="diff-line added">
                            <div class="diff-line-number">+</div>
                            <div class="diff-line-content">          <span class="function">setError</span>(<span class="variable">err</span>.<span class="variable">message</span>);</div>
                        </div>
                        
                        <div class="diff-line added">
                            <div class="diff-line-number">+</div>
                            <div class="diff-line-content">          <span class="function">setLoading</span>(<span class="keyword">false</span>);</div>
                        </div>
                        
                        <div class="diff-line added">
                            <div class="diff-line-number">+</div>
                            <div class="diff-line-content">        });</div>
                        </div>
                        
                        <div class="diff-line context">
                            <div class="diff-line-number">48</div>
                            <div class="diff-line-content">    }, []);</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <footer class="footer">
                Obby Note Monitor • Solarized Dark Theme • Real-time file watching active
            </footer>
        </main>
    </div>
</body>
</html>