<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Obby - Note Monitor</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        :root {
            --md-primary: #2196f3;
            --md-primary-variant: #1976d2;
            --md-secondary: #03dac6;
            --md-background: #121212;
            --md-surface: #1e1e1e;
            --md-surface-variant: #2d2d2d;
            --md-surface-elevated: #242424;
            --md-on-primary: #ffffff;
            --md-on-secondary: #000000;
            --md-on-background: #ffffff;
            --md-on-surface: #ffffff;
            --md-on-surface-variant: #e0e0e0;
            --md-outline: #8a8a8a;
            --md-outline-variant: #3a3a3a;
            --md-error: #cf6679;
            --md-warning: #ff9800;
            --md-success: #4caf50;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: var(--md-background);
            color: var(--md-on-background);
            line-height: 1.5;
        }

        .app-container {
            display: flex;
            height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            width: 280px;
            background-color: var(--md-surface);
            border-right: 1px solid var(--md-outline-variant);
            display: flex;
            flex-direction: column;
            box-shadow: 0 8px 10px -5px rgba(0,0,0,0.2), 0 16px 24px 2px rgba(0,0,0,0.14), 0 6px 30px 5px rgba(0,0,0,0.12);
        }

        .app-header {
            padding: 24px;
            border-bottom: 1px solid var(--md-outline-variant);
        }

        .app-title {
            font-size: 24px;
            font-weight: 500;
            color: var(--md-primary);
            margin-bottom: 8px;
        }

        .app-subtitle {
            font-size: 14px;
            color: var(--md-on-surface-variant);
            opacity: 0.8;
        }

        .nav-menu {
            flex: 1;
            padding: 16px 0;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: var(--md-on-surface);
            text-decoration: none;
            transition: background-color 0.2s;
            cursor: pointer;
        }

        .nav-item:hover {
            background-color: rgba(255, 255, 255, 0.04);
        }

        .nav-item.active {
            background-color: rgba(33, 150, 243, 0.12);
            color: var(--md-primary);
        }

        .nav-item .material-icons {
            margin-right: 16px;
            font-size: 20px;
        }

        .nav-item-text {
            font-size: 14px;
            font-weight: 500;
        }

        .nav-badge {
            margin-left: auto;
            background-color: var(--md-primary);
            color: var(--md-on-primary);
            border-radius: 12px;
            padding: 2px 8px;
            font-size: 12px;
            font-weight: 500;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* Top App Bar */
        .top-app-bar {
            background-color: var(--md-surface-elevated);
            padding: 16px 24px;
            border-bottom: 1px solid var(--md-outline-variant);
            display: flex;
            align-items: center;
            gap: 16px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
        }

        .search-container {
            position: relative;
            flex: 1;
            max-width: 600px;
        }

        .search-input {
            width: 100%;
            padding: 12px 16px 12px 48px;
            background-color: var(--md-surface-variant);
            border: 1px solid var(--md-outline-variant);
            border-radius: 24px;
            color: var(--md-on-surface);
            font-size: 14px;
            outline: none;
            transition: all 0.2s;
        }

        .search-input:focus {
            border-color: var(--md-primary);
            box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
        }

        .search-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--md-on-surface-variant);
        }

        .toolbar-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .icon-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: none;
            background-color: transparent;
            color: var(--md-on-surface);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
        }

        .icon-button:hover {
            background-color: rgba(255, 255, 255, 0.08);
        }

        .icon-button.primary {
            background-color: var(--md-primary);
            color: var(--md-on-primary);
        }

        .icon-button.primary:hover {
            background-color: var(--md-primary-variant);
        }

        /* Content Area */
        .content-area {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }

        /* Cards */
        .card {
            background-color: var(--md-surface-elevated);
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1), 0 8px 16px rgba(0,0,0,0.1);
            margin-bottom: 16px;
            overflow: hidden;
            transition: box-shadow 0.2s;
        }

        .card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.15), 0 12px 24px rgba(0,0,0,0.15);
        }

        .card-header {
            padding: 20px 24px 16px;
            border-bottom: 1px solid var(--md-outline-variant);
        }

        .card-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 4px;
        }

        .card-subtitle {
            font-size: 14px;
            color: var(--md-on-surface-variant);
            opacity: 0.8;
        }

        .card-content {
            padding: 24px;
        }

        /* File Event Cards */
        .file-event {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            padding: 16px 24px;
            border-bottom: 1px solid var(--md-outline-variant);
        }

        .file-event:last-child {
            border-bottom: none;
        }

        .event-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .event-icon.created {
            background-color: rgba(76, 175, 80, 0.1);
            color: var(--md-success);
        }

        .event-icon.modified {
            background-color: rgba(33, 150, 243, 0.1);
            color: var(--md-primary);
        }

        .event-icon.deleted {
            background-color: rgba(207, 102, 121, 0.1);
            color: var(--md-error);
        }

        .event-content {
            flex: 1;
        }

        .event-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 4px;
        }

        .event-path {
            font-size: 14px;
            color: var(--md-on-surface-variant);
            opacity: 0.8;
            margin-bottom: 8px;
            font-family: 'Courier New', monospace;
        }

        .event-meta {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 12px;
            color: var(--md-on-surface-variant);
            opacity: 0.7;
        }

        .event-time {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .event-size {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        /* Chips */
        .chip {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            background-color: var(--md-surface-variant);
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
            color: var(--md-on-surface-variant);
            margin-right: 8px;
            margin-bottom: 8px;
        }

        .chip.primary {
            background-color: rgba(33, 150, 243, 0.12);
            color: var(--md-primary);
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 32px;
        }

        .stat-card {
            background-color: var(--md-surface-elevated);
            padding: 24px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1), 0 8px 16px rgba(0,0,0,0.1);
        }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: var(--md-primary);
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            color: var(--md-on-surface-variant);
            opacity: 0.8;
        }

        /* Floating Action Button */
        .fab {
            position: fixed;
            bottom: 24px;
            right: 24px;
            width: 56px;
            height: 56px;
            border-radius: 16px;
            background-color: var(--md-primary);
            color: var(--md-on-primary);
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 6px 10px rgba(0,0,0,0.2), 0 16px 24px rgba(0,0,0,0.14);
            transition: all 0.2s;
        }

        .fab:hover {
            background-color: var(--md-primary-variant);
            box-shadow: 0 8px 14px rgba(0,0,0,0.25), 0 20px 32px rgba(0,0,0,0.18);
        }

        /* Status Indicators */
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-indicator.online {
            background-color: rgba(76, 175, 80, 0.1);
            color: var(--md-success);
        }

        .status-indicator.offline {
            background-color: rgba(158, 158, 158, 0.1);
            color: var(--md-on-surface-variant);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: currentColor;
        }

        /* Scrollbar Styling */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--md-surface);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--md-outline);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--md-on-surface-variant);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -280px;
                z-index: 1000;
                transition: left 0.3s;
            }

            .sidebar.open {
                left: 0;
            }

            .main-content {
                margin-left: 0;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <nav class="sidebar">
            <div class="app-header">
                <div class="app-title">Obby</div>
                <div class="app-subtitle">Note Monitor</div>
            </div>
            
            <div class="nav-menu">
                <a href="#" class="nav-item active">
                    <span class="material-icons">dashboard</span>
                    <span class="nav-item-text">Dashboard</span>
                </a>
                
                <a href="#" class="nav-item">
                    <span class="material-icons">description</span>
                    <span class="nav-item-text">Recent Changes</span>
                    <span class="nav-badge">12</span>
                </a>
                
                <a href="#" class="nav-item">
                    <span class="material-icons">search</span>
                    <span class="nav-item-text">Search</span>
                </a>
                
                <a href="#" class="nav-item">
                    <span class="material-icons">folder</span>
                    <span class="nav-item-text">Watched Folders</span>
                </a>
                
                <a href="#" class="nav-item">
                    <span class="material-icons">analytics</span>
                    <span class="nav-item-text">Analytics</span>
                </a>
                
                <a href="#" class="nav-item">
                    <span class="material-icons">settings</span>
                    <span class="nav-item-text">Settings</span>
                </a>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Top App Bar -->
            <header class="top-app-bar">
                <div class="search-container">
                    <span class="material-icons search-icon">search</span>
                    <input type="text" class="search-input" placeholder="Search files, content, or changes...">
                </div>
                
                <div class="toolbar-actions">
                    <button class="icon-button">
                        <span class="material-icons">filter_list</span>
                    </button>
                    <button class="icon-button">
                        <span class="material-icons">refresh</span>
                    </button>
                    <button class="icon-button primary">
                        <span class="material-icons">add</span>
                    </button>
                </div>
            </header>

            <!-- Content Area -->
            <div class="content-area">
                <!-- Stats Grid -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value">247</div>
                        <div class="stat-label">Files Monitored</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">18</div>
                        <div class="stat-label">Changes Today</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">5</div>
                        <div class="stat-label">Active Folders</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">2.3MB</div>
                        <div class="stat-label">Data Processed</div>
                    </div>
                </div>

                <!-- System Status Card -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">System Status</div>
                        <div class="card-subtitle">Monitoring service health</div>
                    </div>
                    <div class="card-content">
                        <div style="display: flex; gap: 16px; align-items: center;">
                            <div class="status-indicator online">
                                <div class="status-dot"></div>
                                File Watcher
                            </div>
                            <div class="status-indicator online">
                                <div class="status-dot"></div>
                                AI Processing
                            </div>
                            <div class="status-indicator online">
                                <div class="status-dot"></div>
                                Database
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Changes Card -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">Recent File Changes</div>
                        <div class="card-subtitle">Latest activity from monitored folders</div>
                    </div>
                    
                    <div class="file-event">
                        <div class="event-icon modified">
                            <span class="material-icons">edit</span>
                        </div>
                        <div class="event-content">
                            <div class="event-title">project-notes.md</div>
                            <div class="event-path">~/Documents/Projects/MyApp/notes/</div>
                            <div style="margin-bottom: 8px;">
                                <span class="chip primary">documentation</span>
                                <span class="chip">markdown</span>
                            </div>
                            <div class="event-meta">
                                <div class="event-time">
                                    <span class="material-icons" style="font-size: 14px;">schedule</span>
                                    2 minutes ago
                                </div>
                                <div class="event-size">
                                    <span class="material-icons" style="font-size: 14px;">data_usage</span>
                                    +342 bytes
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="file-event">
                        <div class="event-icon created">
                            <span class="material-icons">add</span>
                        </div>
                        <div class="event-content">
                            <div class="event-title">api-endpoints.ts</div>
                            <div class="event-path">~/Documents/Projects/MyApp/src/types/</div>
                            <div style="margin-bottom: 8px;">
                                <span class="chip primary">typescript</span>
                                <span class="chip">api</span>
                            </div>
                            <div class="event-meta">
                                <div class="event-time">
                                    <span class="material-icons" style="font-size: 14px;">schedule</span>
                                    5 minutes ago
                                </div>
                                <div class="event-size">
                                    <span class="material-icons" style="font-size: 14px;">data_usage</span>
                                    1.2KB
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="file-event">
                        <div class="event-icon modified">
                            <span class="material-icons">edit</span>
                        </div>
                        <div class="event-content">
                            <div class="event-title">database.py</div>
                            <div class="event-path">~/Documents/Projects/MyApp/backend/</div>
                            <div style="margin-bottom: 8px;">
                                <span class="chip primary">python</span>
                                <span class="chip">database</span>
                            </div>
                            <div class="event-meta">
                                <div class="event-time">
                                    <span class="material-icons" style="font-size: 14px;">schedule</span>
                                    12 minutes ago
                                </div>
                                <div class="event-size">
                                    <span class="material-icons" style="font-size: 14px;">data_usage</span>
                                    +89 bytes
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="file-event">
                        <div class="event-icon deleted">
                            <span class="material-icons">delete</span>
                        </div>
                        <div class="event-content">
                            <div class="event-title">temp-backup.sql</div>
                            <div class="event-path">~/Documents/Projects/MyApp/backup/</div>
                            <div style="margin-bottom: 8px;">
                                <span class="chip">sql</span>
                                <span class="chip">temporary</span>
                            </div>
                            <div class="event-meta">
                                <div class="event-time">
                                    <span class="material-icons" style="font-size: 14px;">schedule</span>
                                    18 minutes ago
                                </div>
                                <div class="event-size">
                                    <span class="material-icons" style="font-size: 14px;">data_usage</span>
                                    -456KB
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI Insights Card -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">AI Insights</div>
                        <div class="card-subtitle">Smart analysis of your recent changes</div>
                    </div>
                    <div class="card-content">
                        <p style="color: var(--md-on-surface-variant); margin-bottom: 16px;">
                            Based on recent activity, you've been focusing on API development and documentation updates. 
                            The TypeScript definitions suggest you're expanding your application's data layer.
                        </p>
                        <div>
                            <span class="chip primary">API Development</span>
                            <span class="chip primary">Documentation</span>
                            <span class="chip">Database Schema</span>
                            <span class="chip">TypeScript</span>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Floating Action Button -->
    <button class="fab">
        <span class="material-icons">add</span>
    </button>

    <script>
        // Simple interactivity for demo
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
                item.classList.add('active');
            });
        });

        // Search input focus effect
        const searchInput = document.querySelector('.search-input');
        searchInput.addEventListener('focus', () => {
            searchInput.parentElement.style.transform = 'scale(1.02)';
        });
        searchInput.addEventListener('blur', () => {
            searchInput.parentElement.style.transform = 'scale(1)';
        });

        // Card hover effects
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-2px)';
            });
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>