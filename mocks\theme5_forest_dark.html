<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Obby - Forest Theme</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --forest-dark: #052e16;
            --forest-medium: #14532d;
            --forest-light: #166534;
            --emerald-accent: #10b981;
            --emerald-light: #34d399;
            --text-primary: #d1fae5;
            --text-secondary: #a7f3d0;
            --text-muted: #6ee7b7;
            --border-color: #065f46;
            --hover-bg: #1a5f3f;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--forest-dark) 0%, var(--forest-medium) 100%);
            color: var(--text-primary);
            min-height: 100vh;
            background-attachment: fixed;
        }

        .app-container {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            width: 280px;
            background: rgba(5, 46, 22, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid var(--border-color);
            padding: 1.5rem;
            overflow-y: auto;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .logo::before {
            content: "🌲";
            font-size: 2rem;
        }

        .logo h1 {
            color: var(--emerald-accent);
            font-size: 1.5rem;
            font-weight: 700;
        }

        .nav-section {
            margin-bottom: 1.5rem;
        }

        .nav-title {
            color: var(--text-secondary);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 0.5rem;
            padding-left: 0.5rem;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 0.25rem;
        }

        .nav-item:hover, .nav-item.active {
            background: var(--hover-bg);
            color: var(--emerald-light);
        }

        .nav-item.active {
            background: linear-gradient(90deg, var(--emerald-accent), var(--emerald-light));
            color: var(--forest-dark);
            font-weight: 600;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            padding: 2rem;
            overflow-y: auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .header h2 {
            color: var(--emerald-accent);
            font-size: 1.75rem;
            font-weight: 700;
        }

        .stats-bar {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: rgba(20, 83, 45, 0.5);
            border-radius: 2rem;
            border: 1px solid var(--border-color);
        }

        .stat-value {
            color: var(--emerald-accent);
            font-weight: 600;
        }

        /* Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .panel {
            background: rgba(20, 83, 45, 0.3);
            border: 1px solid var(--border-color);
            border-radius: 1rem;
            padding: 1.5rem;
            backdrop-filter: blur(5px);
        }

        .panel-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid var(--border-color);
        }

        .panel-title {
            color: var(--emerald-accent);
            font-size: 1.125rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* File Tree */
        .file-tree {
            list-style: none;
        }

        .file-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 0;
            border-radius: 0.25rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .file-item:hover {
            background: var(--hover-bg);
            padding-left: 0.5rem;
        }

        .file-icon {
            width: 1rem;
            height: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .folder {
            color: var(--emerald-accent);
        }

        .file {
            color: var(--text-muted);
        }

        .file-item.modified {
            color: var(--emerald-light);
        }

        .file-item.modified::after {
            content: "●";
            color: var(--emerald-accent);
            margin-left: auto;
        }

        /* Change Log */
        .change-log {
            max-height: 300px;
            overflow-y: auto;
        }

        .change-item {
            display: flex;
            gap: 1rem;
            padding: 0.75rem;
            border-radius: 0.5rem;
            margin-bottom: 0.5rem;
            background: rgba(5, 46, 22, 0.3);
            border-left: 3px solid var(--emerald-accent);
        }

        .change-time {
            color: var(--text-muted);
            font-size: 0.875rem;
            min-width: 60px;
        }

        .change-details {
            flex: 1;
        }

        .change-file {
            color: var(--emerald-light);
            font-weight: 500;
        }

        .change-action {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        /* Activity Feed */
        .activity-feed {
            grid-column: 1 / -1;
        }

        .activity-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: rgba(5, 46, 22, 0.2);
            border-radius: 0.75rem;
            margin-bottom: 0.75rem;
            border: 1px solid var(--border-color);
        }

        .activity-icon {
            width: 2.5rem;
            height: 2.5rem;
            background: var(--emerald-accent);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            color: var(--text-primary);
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .activity-subtitle {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .activity-time {
            color: var(--text-muted);
            font-size: 0.75rem;
            min-width: 80px;
        }

        /* Search Bar */
        .search-container {
            position: relative;
            margin-bottom: 1rem;
        }

        .search-input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            background: rgba(20, 83, 45, 0.3);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            color: var(--text-primary);
            font-size: 0.875rem;
        }

        .search-input::placeholder {
            color: var(--text-muted);
        }

        .search-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
        }

        /* Nature-inspired decorations */
        .nature-decoration {
            position: fixed;
            opacity: 0.1;
            pointer-events: none;
            z-index: -1;
        }

        .leaf-1 {
            top: 10%;
            right: 5%;
            font-size: 3rem;
            animation: float 6s ease-in-out infinite;
        }

        .leaf-2 {
            bottom: 20%;
            left: 10%;
            font-size: 2rem;
            animation: float 8s ease-in-out infinite reverse;
        }

        .tree-1 {
            top: 60%;
            right: 15%;
            font-size: 4rem;
            animation: sway 10s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(5deg); }
        }

        @keyframes sway {
            0%, 100% { transform: rotate(-2deg); }
            50% { transform: rotate(2deg); }
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--forest-dark);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--emerald-accent);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--emerald-light);
        }
    </style>
</head>
<body>
    <!-- Nature decorations -->
    <div class="nature-decoration leaf-1">🍃</div>
    <div class="nature-decoration leaf-2">🌿</div>
    <div class="nature-decoration tree-1">🌲</div>

    <div class="app-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="logo">
                <h1>Obby Forest</h1>
            </div>

            <nav>
                <div class="nav-section">
                    <div class="nav-title">Monitor</div>
                    <div class="nav-item active">
                        <span>📊</span>
                        Dashboard
                    </div>
                    <div class="nav-item">
                        <span>📁</span>
                        File Explorer
                    </div>
                    <div class="nav-item">
                        <span>🔍</span>
                        Search
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-title">Activity</div>
                    <div class="nav-item">
                        <span>📝</span>
                        Recent Changes
                    </div>
                    <div class="nav-item">
                        <span>🌱</span>
                        Living Notes
                    </div>
                    <div class="nav-item">
                        <span>📈</span>
                        Analytics
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-title">Settings</div>
                    <div class="nav-item">
                        <span>⚙️</span>
                        Configuration
                    </div>
                    <div class="nav-item">
                        <span>🎨</span>
                        Themes
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <h2>🌲 Forest Monitor Dashboard</h2>
                <div class="stats-bar">
                    <div class="stat-item">
                        <span>📊</span>
                        <span class="stat-value">24</span>
                        <span>Files Watched</span>
                    </div>
                    <div class="stat-item">
                        <span>🔄</span>
                        <span class="stat-value">7</span>
                        <span>Recent Changes</span>
                    </div>
                    <div class="stat-item">
                        <span>🌱</span>
                        <span class="stat-value">Active</span>
                    </div>
                </div>
            </header>

            <div class="dashboard-grid">
                <!-- File Tree Panel -->
                <div class="panel">
                    <div class="panel-header">
                        <div class="panel-title">
                            <span>🌳</span>
                            Project Tree
                        </div>
                    </div>
                    <div class="search-container">
                        <input type="text" class="search-input" placeholder="Filter files...">
                        <span class="search-icon">🔍</span>
                    </div>
                    <ul class="file-tree">
                        <li class="file-item">
                            <div class="file-icon folder">📁</div>
                            <span>src/</span>
                        </li>
                        <li class="file-item modified" style="margin-left: 1rem;">
                            <div class="file-icon file">📄</div>
                            <span>main.py</span>
                        </li>
                        <li class="file-item" style="margin-left: 1rem;">
                            <div class="file-icon file">📄</div>
                            <span>config.py</span>
                        </li>
                        <li class="file-item">
                            <div class="file-icon folder">📁</div>
                            <span>docs/</span>
                        </li>
                        <li class="file-item modified" style="margin-left: 1rem;">
                            <div class="file-icon file">📝</div>
                            <span>README.md</span>
                        </li>
                        <li class="file-item" style="margin-left: 1rem;">
                            <div class="file-icon file">📝</div>
                            <span>guide.md</span>
                        </li>
                        <li class="file-item">
                            <div class="file-icon folder">📁</div>
                            <span>tests/</span>
                        </li>
                        <li class="file-item" style="margin-left: 1rem;">
                            <div class="file-icon file">🧪</div>
                            <span>test_main.py</span>
                        </li>
                    </ul>
                </div>

                <!-- Change Log Panel -->
                <div class="panel">
                    <div class="panel-header">
                        <div class="panel-title">
                            <span>📋</span>
                            Recent Changes
                        </div>
                    </div>
                    <div class="change-log">
                        <div class="change-item">
                            <div class="change-time">2:34 PM</div>
                            <div class="change-details">
                                <div class="change-file">src/main.py</div>
                                <div class="change-action">Modified - Added error handling</div>
                            </div>
                        </div>
                        <div class="change-item">
                            <div class="change-time">2:28 PM</div>
                            <div class="change-details">
                                <div class="change-file">docs/README.md</div>
                                <div class="change-action">Modified - Updated installation guide</div>
                            </div>
                        </div>
                        <div class="change-item">
                            <div class="change-time">2:15 PM</div>
                            <div class="change-details">
                                <div class="change-file">config.json</div>
                                <div class="change-action">Modified - Changed theme settings</div>
                            </div>
                        </div>
                        <div class="change-item">
                            <div class="change-time">1:45 PM</div>
                            <div class="change-details">
                                <div class="change-file">src/utils.py</div>
                                <div class="change-action">Created - New utility functions</div>
                            </div>
                        </div>
                        <div class="change-item">
                            <div class="change-time">1:23 PM</div>
                            <div class="change-details">
                                <div class="change-file">tests/test_main.py</div>
                                <div class="change-action">Modified - Added new test cases</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Activity Feed -->
                <div class="panel activity-feed">
                    <div class="panel-header">
                        <div class="panel-title">
                            <span>🌿</span>
                            Activity Feed
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">🌱</div>
                        <div class="activity-content">
                            <div class="activity-title">Living Note Updated</div>
                            <div class="activity-subtitle">Project Goals note was automatically updated with new insights</div>
                        </div>
                        <div class="activity-time">5 min ago</div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">🔍</div>
                        <div class="activity-content">
                            <div class="activity-title">Semantic Search Indexed</div>
                            <div class="activity-subtitle">3 new files processed and added to search index</div>
                        </div>
                        <div class="activity-time">12 min ago</div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">📊</div>
                        <div class="activity-content">
                            <div class="activity-title">Monitoring Started</div>
                            <div class="activity-subtitle">File system monitoring activated for /src directory</div>
                        </div>
                        <div class="activity-time">1 hour ago</div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">🎨</div>
                        <div class="activity-content">
                            <div class="activity-title">Theme Applied</div>
                            <div class="activity-subtitle">Forest Dark theme successfully activated</div>
                        </div>
                        <div class="activity-time">2 hours ago</div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>