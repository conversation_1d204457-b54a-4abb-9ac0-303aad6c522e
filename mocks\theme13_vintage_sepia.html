<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Obby - Vintage Sepia Note Monitor</title>
    <link href="https://fonts.googleapis.com/css2?family=Crimson+Text:ital,wght@0,400;0,600;1,400&family=Playfair+Display:wght@400;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Crimson Text', serif;
            background: #f4f1e8;
            color: #8b7355;
            line-height: 1.6;
            min-height: 100vh;
            position: relative;
        }

        /* Paper texture overlay */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(circle at 25% 25%, rgba(139, 115, 85, 0.02) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(139, 115, 85, 0.02) 0%, transparent 50%),
                linear-gradient(45deg, transparent 49%, rgba(139, 115, 85, 0.01) 50%, transparent 51%);
            pointer-events: none;
            z-index: 1;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
            z-index: 2;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #e8e1d4 0%, #ddd4c4 100%);
            border: 2px solid #c4b29a;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 4px 12px rgba(139, 115, 85, 0.15);
            position: relative;
        }

        .header::after {
            content: '';
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            border: 1px solid #c4b29a;
            border-radius: 4px;
            pointer-events: none;
        }

        .header h1 {
            font-family: 'Playfair Display', serif;
            font-size: 2.5rem;
            color: #6b5b47;
            text-align: center;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(139, 115, 85, 0.1);
        }

        .header .subtitle {
            text-align: center;
            font-style: italic;
            color: #8b7355;
            font-size: 1.1rem;
        }

        /* Navigation */
        .nav {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }

        .nav-item {
            background: #e8e1d4;
            border: 2px solid #c4b29a;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            color: #6b5b47;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            box-shadow: 0 2px 6px rgba(139, 115, 85, 0.1);
        }

        .nav-item:hover {
            background: #ddd4c4;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(139, 115, 85, 0.2);
        }

        .nav-item.active {
            background: #c4b29a;
            color: #f4f1e8;
        }

        /* Main content area */
        .main-content {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 30px;
        }

        /* Content panel */
        .content-panel {
            background: #f9f6ef;
            border: 2px solid #c4b29a;
            border-radius: 8px;
            padding: 25px;
            box-shadow: 0 4px 12px rgba(139, 115, 85, 0.1);
            position: relative;
        }

        .content-panel::before {
            content: '';
            position: absolute;
            top: 15px;
            left: 15px;
            right: 15px;
            bottom: 15px;
            border: 1px dashed #c4b29a;
            border-radius: 4px;
            pointer-events: none;
        }

        .panel-title {
            font-family: 'Playfair Display', serif;
            font-size: 1.8rem;
            color: #6b5b47;
            margin-bottom: 20px;
            border-bottom: 2px solid #c4b29a;
            padding-bottom: 10px;
        }

        /* File monitoring section */
        .file-item {
            background: #f4f1e8;
            border: 1px solid #d4c7b5;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
            position: relative;
        }

        .file-item::before {
            content: '📜';
            position: absolute;
            top: 15px;
            left: 15px;
            font-size: 1.2rem;
        }

        .file-item:hover {
            background: #f0ede5;
            border-color: #c4b29a;
            transform: translateX(5px);
        }

        .file-name {
            font-weight: 600;
            color: #6b5b47;
            margin-left: 35px;
            margin-bottom: 5px;
        }

        .file-path {
            font-size: 0.9rem;
            color: #8b7355;
            margin-left: 35px;
            margin-bottom: 8px;
            font-style: italic;
        }

        .file-status {
            margin-left: 35px;
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: 600;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-modified {
            background: #d4a574;
            color: #f4f1e8;
        }

        .status-created {
            background: #9b8a6b;
            color: #f4f1e8;
        }

        .status-watching {
            background: #c4b29a;
            color: #f4f1e8;
        }

        /* Sidebar */
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .sidebar-panel {
            background: #f9f6ef;
            border: 2px solid #c4b29a;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(139, 115, 85, 0.1);
        }

        .sidebar-title {
            font-family: 'Playfair Display', serif;
            font-size: 1.3rem;
            color: #6b5b47;
            margin-bottom: 15px;
            border-bottom: 1px solid #c4b29a;
            padding-bottom: 8px;
        }

        /* Statistics */
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px dotted #d4c7b5;
        }

        .stat-item:last-child {
            border-bottom: none;
        }

        .stat-value {
            font-weight: 600;
            color: #6b5b47;
            background: #e8e1d4;
            padding: 2px 8px;
            border-radius: 4px;
            border: 1px solid #c4b29a;
        }

        /* Search box */
        .search-box {
            width: 100%;
            padding: 12px;
            border: 2px solid #c4b29a;
            border-radius: 6px;
            background: #f4f1e8;
            color: #6b5b47;
            font-family: 'Crimson Text', serif;
            font-size: 1rem;
        }

        .search-box:focus {
            outline: none;
            border-color: #8b7355;
            background: #f9f6ef;
            box-shadow: 0 0 8px rgba(139, 115, 85, 0.2);
        }

        /* Recent activity */
        .activity-item {
            padding: 10px 0;
            border-bottom: 1px dotted #d4c7b5;
            font-size: 0.9rem;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-time {
            color: #8b7355;
            font-style: italic;
            font-size: 0.8rem;
        }

        .activity-action {
            color: #6b5b47;
            font-weight: 600;
        }

        /* Vintage decorative elements */
        .ornament {
            text-align: center;
            font-size: 1.5rem;
            color: #c4b29a;
            margin: 20px 0;
        }

        /* Vintage scroll indicator */
        .scroll-indicator {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 100px;
            background: linear-gradient(to bottom, #c4b29a, #8b7355);
            border-radius: 4px;
            opacity: 0.6;
            box-shadow: 0 2px 6px rgba(139, 115, 85, 0.2);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .nav {
                flex-wrap: wrap;
                gap: 10px;
            }
            
            .nav-item {
                padding: 8px 16px;
                font-size: 0.9rem;
            }
        }

        /* Loading animation for vintage feel */
        .loading-dots {
            display: inline-block;
        }

        .loading-dots::after {
            content: '';
            animation: dots 2s infinite;
        }

        @keyframes dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }
    </style>
</head>
<body>
    <div class="scroll-indicator"></div>
    
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>Obby</h1>
            <p class="subtitle">Vintage Document Monitoring & Archive System</p>
        </header>

        <!-- Navigation -->
        <nav class="nav">
            <a href="#" class="nav-item active">Monitor</a>
            <a href="#" class="nav-item">Archive</a>
            <a href="#" class="nav-item">Search</a>
            <a href="#" class="nav-item">Reports</a>
            <a href="#" class="nav-item">Settings</a>
        </nav>

        <!-- Main content -->
        <div class="main-content">
            <!-- Main panel -->
            <div class="content-panel">
                <h2 class="panel-title">Document Observatory</h2>
                
                <div class="ornament">❦ ❦ ❦</div>
                
                <!-- File monitoring items -->
                <div class="file-item">
                    <div class="file-name">research_notes.md</div>
                    <div class="file-path">~/Documents/Research/</div>
                    <div class="file-status">
                        <span class="status-badge status-modified">Modified</span>
                        <span>2 minutes ago</span>
                    </div>
                </div>

                <div class="file-item">
                    <div class="file-name">manuscript_draft_v3.txt</div>
                    <div class="file-path">~/Writing/Novel/</div>
                    <div class="file-status">
                        <span class="status-badge status-created">Created</span>
                        <span>15 minutes ago</span>
                    </div>
                </div>

                <div class="file-item">
                    <div class="file-name">correspondence.log</div>
                    <div class="file-path">~/Personal/Letters/</div>
                    <div class="file-status">
                        <span class="status-badge status-watching">Watching</span>
                        <span>Active monitoring</span>
                    </div>
                </div>

                <div class="file-item">
                    <div class="file-name">journal_entry_july_1923.md</div>
                    <div class="file-path">~/Personal/Journal/</div>
                    <div class="file-status">
                        <span class="status-badge status-modified">Modified</span>
                        <span>1 hour ago</span>
                    </div>
                </div>

                <div class="file-item">
                    <div class="file-name">bibliography.bib</div>
                    <div class="file-path">~/Academic/References/</div>
                    <div class="file-status">
                        <span class="status-badge status-watching">Watching</span>
                        <span>Active monitoring</span>
                    </div>
                </div>

                <div class="ornament">❦ ❦ ❦</div>
                
                <p style="text-align: center; font-style: italic; color: #8b7355; margin-top: 20px;">
                    "The written word endures through time, and we are its faithful guardians."
                </p>
            </div>

            <!-- Sidebar -->
            <aside class="sidebar">
                <!-- Search -->
                <div class="sidebar-panel">
                    <h3 class="sidebar-title">Document Search</h3>
                    <input type="text" class="search-box" placeholder="Search archives...">
                </div>

                <!-- Statistics -->
                <div class="sidebar-panel">
                    <h3 class="sidebar-title">Archive Statistics</h3>
                    <div class="stat-item">
                        <span>Documents Monitored</span>
                        <span class="stat-value">247</span>
                    </div>
                    <div class="stat-item">
                        <span>Recent Changes</span>
                        <span class="stat-value">18</span>
                    </div>
                    <div class="stat-item">
                        <span>Active Watchers</span>
                        <span class="stat-value">12</span>
                    </div>
                    <div class="stat-item">
                        <span>Archive Size</span>
                        <span class="stat-value">2.4 GB</span>
                    </div>
                </div>

                <!-- Recent activity -->
                <div class="sidebar-panel">
                    <h3 class="sidebar-title">Recent Activity</h3>
                    <div class="activity-item">
                        <div class="activity-action">Modified: research_notes.md</div>
                        <div class="activity-time">2 minutes ago</div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-action">Created: manuscript_draft_v3.txt</div>
                        <div class="activity-time">15 minutes ago</div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-action">Deleted: temp_notes.tmp</div>
                        <div class="activity-time">1 hour ago</div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-action">Modified: journal_entry_july_1923.md</div>
                        <div class="activity-time">1 hour ago</div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-action">System: Backup completed</div>
                        <div class="activity-time">2 hours ago</div>
                    </div>
                </div>

                <!-- Status -->
                <div class="sidebar-panel">
                    <h3 class="sidebar-title">System Status</h3>
                    <div class="stat-item">
                        <span>Monitoring</span>
                        <span class="stat-value" style="background: #9b8a6b;">Active</span>
                    </div>
                    <div class="stat-item">
                        <span>Last Scan</span>
                        <span class="stat-value">Just now</span>
                    </div>
                    <div class="stat-item">
                        <span>Connection</span>
                        <span class="stat-value" style="background: #9b8a6b;">Stable</span>
                    </div>
                </div>
            </aside>
        </div>
    </div>

    <script>
        // Add some vintage-style animations
        document.addEventListener('DOMContentLoaded', function() {
            // Simulate real-time updates
            const fileItems = document.querySelectorAll('.file-item');
            
            setInterval(() => {
                const randomItem = fileItems[Math.floor(Math.random() * fileItems.length)];
                randomItem.style.background = '#f0ede5';
                setTimeout(() => {
                    randomItem.style.background = '#f4f1e8';
                }, 1000);
            }, 5000);

            // Add loading dots to active monitoring items
            const watchingBadges = document.querySelectorAll('.status-watching');
            watchingBadges.forEach(badge => {
                const parent = badge.parentElement;
                const statusText = parent.querySelector('span:last-child');
                if (statusText && statusText.textContent === 'Active monitoring') {
                    statusText.innerHTML = 'Active monitoring<span class="loading-dots"></span>';
                }
            });
        });
    </script>
</body>
</html>