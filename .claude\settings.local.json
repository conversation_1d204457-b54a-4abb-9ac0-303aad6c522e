{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(python:*)", "Bash(copy \"D:\\Python Projects\\obby\\main.py\" \"D:\\Python Projects\\obby\\legacy\\main_cli.py\")", "Bash(cp:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm install)", "Bash(npm run dev:*)", "Bash(rg:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(dir)", "Bash(start /b python:*)", "<PERSON><PERSON>(timeout:*)", "Bash(type:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(grep:*)", "Bash(npm run lint)", "<PERSON><PERSON>(dir:*)", "mcp__ide__getDiagnostics"], "deny": []}, "outputStyle": "Explanatory"}