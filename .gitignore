# Python
__pycache__/
*.py[cod]
*$py.class
*.pyc
*.pyd
*.pyo
*.so
.Python
build/
dist/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.pytest_cache/
.mypy_cache/
.coverage
htmlcov/
.pytest_cache/
.coverage.*
*.cover
*.py,cover

# Virtual Environment
.env
.venv/
venv/
ENV/
env/
.env.*.local
!.env.example

# Project specific
notes/
obby.db
obby.db-shm
obby.db-wal
*.sqlite
*.sqlite3

# VS Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Node/React
node_modules/
/.pnp
.pnp.js

# Build files
/build
/dist

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor directories and files
.idea/
.DS_Store
._*
.Spotlight-V100
.Trashes
Thumbs.db
ehthumbs.db
Desktop.ini
*~
*.swp
*.swo
*.sublime-*

# Testing
/coverage
.nyc_output

# Local development
.env.local
.env.development.local
.env.test.local
.env.production.local

# Project specific
snapshots/*
database/schema.sql
.codeiumignore
format.md
notes/living_note.md
notes/test.md
utils/.obbyignore
ouput/*
output/summaries/*
GEMINI.md
output/daily/Living Note - 2025-08-15.md
