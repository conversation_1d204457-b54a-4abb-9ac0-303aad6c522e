<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Obby - File Monitor | Nord Theme</title>
    <style>
        /* Nord Color Palette */
        :root {
            /* Polar Night */
            --nord0: #2e3440;  /* Background */
            --nord1: #3b4252;  /* Surface */
            --nord2: #434c5e;  /* Surface variant */
            --nord3: #4c566a;  /* Surface variant 2 */
            
            /* Snow Storm */
            --nord4: #d8dee9;  /* Text primary */
            --nord5: #e5e9f0;  /* Text secondary */
            --nord6: #eceff4;  /* Text high contrast */
            
            /* Frost */
            --nord7: #8fbcbb;  /* Frost teal */
            --nord8: #88c0d0;  /* Frost blue */
            --nord9: #81a1c1;  /* Frost blue 2 */
            --nord10: #5e81ac; /* Frost blue 3 */
            
            /* Aurora */
            --nord11: #bf616a; /* Red */
            --nord12: #d08770; /* Orange */
            --nord13: #ebcb8b; /* Yellow */
            --nord14: #a3be8c; /* Green */
            --nord15: #b48ead; /* Purple */
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
            background: var(--nord0);
            color: var(--nord4);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            background: var(--nord1);
            padding: 1rem 2rem;
            border-bottom: 2px solid var(--nord2);
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--nord8);
        }

        .logo::before {
            content: "❄️";
            font-size: 1.8rem;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: var(--nord2);
            border-radius: 6px;
            font-size: 0.9rem;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: var(--nord14);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Main Layout */
        .container {
            display: grid;
            grid-template-columns: 300px 1fr;
            height: calc(100vh - 80px);
        }

        /* Sidebar */
        .sidebar {
            background: var(--nord1);
            border-right: 2px solid var(--nord2);
            padding: 1.5rem;
            overflow-y: auto;
        }

        .sidebar-section {
            margin-bottom: 2rem;
        }

        .sidebar-title {
            color: var(--nord8);
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .watched-folders {
            list-style: none;
        }

        .folder-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            margin-bottom: 0.25rem;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.85rem;
        }

        .folder-item:hover {
            background: var(--nord2);
            color: var(--nord5);
        }

        .folder-item.active {
            background: var(--nord10);
            color: var(--nord6);
        }

        .folder-icon {
            color: var(--nord8);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.75rem;
        }

        .stat-card {
            background: var(--nord2);
            padding: 0.75rem;
            border-radius: 6px;
            text-align: center;
        }

        .stat-number {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--nord8);
        }

        .stat-label {
            font-size: 0.7rem;
            color: var(--nord4);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Main Content */
        .main-content {
            background: var(--nord0);
            display: flex;
            flex-direction: column;
        }

        /* Controls */
        .controls {
            background: var(--nord1);
            padding: 1rem 2rem;
            border-bottom: 1px solid var(--nord2);
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .search-box {
            flex: 1;
            max-width: 400px;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            background: var(--nord2);
            border: 1px solid var(--nord3);
            border-radius: 6px;
            color: var(--nord4);
            font-family: inherit;
            font-size: 0.9rem;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--nord8);
            box-shadow: 0 0 0 3px rgba(94, 129, 172, 0.2);
        }

        .search-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--nord3);
        }

        .filter-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .filter-btn {
            padding: 0.5rem 1rem;
            background: var(--nord2);
            border: 1px solid var(--nord3);
            border-radius: 4px;
            color: var(--nord4);
            font-family: inherit;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .filter-btn:hover {
            background: var(--nord3);
        }

        .filter-btn.active {
            background: var(--nord10);
            color: var(--nord6);
            border-color: var(--nord10);
        }

        /* Activity Feed */
        .activity-feed {
            flex: 1;
            padding: 1.5rem 2rem;
            overflow-y: auto;
        }

        .activity-item {
            background: var(--nord1);
            border: 1px solid var(--nord2);
            border-radius: 8px;
            padding: 1.25rem;
            margin-bottom: 1rem;
            transition: all 0.2s ease;
        }

        .activity-item:hover {
            border-color: var(--nord8);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .activity-header {
            display: flex;
            justify-content: between;
            align-items: flex-start;
            margin-bottom: 0.75rem;
        }

        .activity-type {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.25rem 0.75rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .activity-type.created { background: var(--nord14); color: var(--nord0); }
        .activity-type.modified { background: var(--nord13); color: var(--nord0); }
        .activity-type.deleted { background: var(--nord11); color: var(--nord6); }

        .activity-time {
            font-size: 0.8rem;
            color: var(--nord3);
            margin-left: auto;
        }

        .activity-file {
            font-family: 'JetBrains Mono', monospace;
            color: var(--nord8);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .activity-path {
            font-size: 0.8rem;
            color: var(--nord3);
            margin-bottom: 0.75rem;
        }

        .activity-diff {
            background: var(--nord0);
            border-radius: 4px;
            padding: 0.75rem;
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.8rem;
            overflow-x: auto;
        }

        .diff-line {
            line-height: 1.4;
        }

        .diff-line.added {
            color: var(--nord14);
            background: rgba(163, 190, 140, 0.1);
        }

        .diff-line.removed {
            color: var(--nord11);
            background: rgba(191, 97, 106, 0.1);
        }

        .diff-line.context {
            color: var(--nord4);
        }

        /* Arctic Decorative Elements */
        .frost-pattern {
            position: fixed;
            top: 0;
            right: 0;
            width: 200px;
            height: 200px;
            background: linear-gradient(45deg, 
                transparent 30%, 
                rgba(136, 192, 208, 0.03) 50%, 
                transparent 70%);
            pointer-events: none;
            z-index: 0;
        }

        .snowflake {
            position: absolute;
            color: var(--nord8);
            font-size: 0.8rem;
            opacity: 0.3;
            animation: float 6s ease-in-out infinite;
        }

        .snowflake:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
        .snowflake:nth-child(2) { top: 60%; left: 80%; animation-delay: 2s; }
        .snowflake:nth-child(3) { top: 80%; left: 20%; animation-delay: 4s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(180deg); }
        }

        /* Scrollbar Styling */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--nord1);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--nord3);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--nord8);
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: var(--nord3);
        }

        .empty-state .snowflake-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <!-- Arctic Decorative Elements -->
    <div class="frost-pattern"></div>
    <div class="snowflake">❄</div>
    <div class="snowflake">❅</div>
    <div class="snowflake">❆</div>

    <!-- Header -->
    <header class="header">
        <div class="logo">Obby Monitor</div>
        <div class="status-indicator">
            <div class="status-dot"></div>
            <span>Watching 4 directories</span>
        </div>
    </header>

    <!-- Main Container -->
    <div class="container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-section">
                <h3 class="sidebar-title">
                    <span>📁</span> Watched Paths
                </h3>
                <ul class="watched-folders">
                    <li class="folder-item active">
                        <span class="folder-icon">📂</span>
                        <span>/home/<USER>/projects</span>
                    </li>
                    <li class="folder-item">
                        <span class="folder-icon">📂</span>
                        <span>/home/<USER>/notes</span>
                    </li>
                    <li class="folder-item">
                        <span class="folder-icon">📂</span>
                        <span>/home/<USER>/configs</span>
                    </li>
                    <li class="folder-item">
                        <span class="folder-icon">📂</span>
                        <span>/home/<USER>/scripts</span>
                    </li>
                </ul>
            </div>

            <div class="sidebar-section">
                <h3 class="sidebar-title">
                    <span>📊</span> Statistics
                </h3>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">247</div>
                        <div class="stat-label">Events Today</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">1.2k</div>
                        <div class="stat-label">Files Tracked</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">12</div>
                        <div class="stat-label">Active Sessions</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">98%</div>
                        <div class="stat-label">Uptime</div>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Controls -->
            <div class="controls">
                <div class="search-box">
                    <span class="search-icon">🔍</span>
                    <input type="text" class="search-input" placeholder="Search files, content, or changes...">
                </div>
                <div class="filter-buttons">
                    <button class="filter-btn active">All</button>
                    <button class="filter-btn">Created</button>
                    <button class="filter-btn">Modified</button>
                    <button class="filter-btn">Deleted</button>
                </div>
            </div>

            <!-- Activity Feed -->
            <div class="activity-feed">
                <div class="activity-item">
                    <div class="activity-header">
                        <span class="activity-type modified">Modified</span>
                        <span class="activity-time">2 minutes ago</span>
                    </div>
                    <div class="activity-file">config.json</div>
                    <div class="activity-path">/home/<USER>/projects/obby/config.json</div>
                    <div class="activity-diff">
                        <div class="diff-line context">  "monitoring": {</div>
                        <div class="diff-line removed">-    "interval": 5000,</div>
                        <div class="diff-line added">+    "interval": 3000,</div>
                        <div class="diff-line context">    "enabled": true</div>
                        <div class="diff-line context">  }</div>
                    </div>
                </div>

                <div class="activity-item">
                    <div class="activity-header">
                        <span class="activity-type created">Created</span>
                        <span class="activity-time">5 minutes ago</span>
                    </div>
                    <div class="activity-file">arctic_theme.css</div>
                    <div class="activity-path">/home/<USER>/projects/themes/arctic_theme.css</div>
                    <div class="activity-diff">
                        <div class="diff-line added">+ /* Nord Color Palette */</div>
                        <div class="diff-line added">+ :root {</div>
                        <div class="diff-line added">+   --nord0: #2e3440;</div>
                        <div class="diff-line added">+   --nord1: #3b4252;</div>
                        <div class="diff-line added">+ }</div>
                    </div>
                </div>

                <div class="activity-item">
                    <div class="activity-header">
                        <span class="activity-type modified">Modified</span>
                        <span class="activity-time">8 minutes ago</span>
                    </div>
                    <div class="activity-file">README.md</div>
                    <div class="activity-path">/home/<USER>/projects/obby/README.md</div>
                    <div class="activity-diff">
                        <div class="diff-line context"># Obby File Monitor</div>
                        <div class="diff-line context"></div>
                        <div class="diff-line added">+ ## Arctic Theme Support</div>
                        <div class="diff-line added">+ Beautiful Nord color palette integration</div>
                        <div class="diff-line context"></div>
                    </div>
                </div>

                <div class="activity-item">
                    <div class="activity-header">
                        <span class="activity-type deleted">Deleted</span>
                        <span class="activity-time">12 minutes ago</span>
                    </div>
                    <div class="activity-file">temp_backup.json</div>
                    <div class="activity-path">/home/<USER>/projects/obby/temp_backup.json</div>
                    <div class="activity-diff">
                        <div class="diff-line removed">- File removed from filesystem</div>
                    </div>
                </div>

                <div class="activity-item">
                    <div class="activity-header">
                        <span class="activity-type modified">Modified</span>
                        <span class="activity-time">15 minutes ago</span>
                    </div>
                    <div class="activity-file">monitor.py</div>
                    <div class="activity-path">/home/<USER>/projects/obby/core/monitor.py</div>
                    <div class="activity-diff">
                        <div class="diff-line context">class ObbyMonitor:</div>
                        <div class="diff-line context">    def __init__(self):</div>
                        <div class="diff-line removed">-        self.theme = "default"</div>
                        <div class="diff-line added">+        self.theme = "nord"</div>
                        <div class="diff-line context">        self.active = True</div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>