# Core dependencies for Obby - Note Change Tracker

# OpenAI API client (for AI-assisted summarization)
openai>=1.0.0

# File system monitoring for instant change detection
watchdog>=3.0.0

# Native file monitoring and version tracking (no git dependency)
# Uses built-in difflib for diff generation

# Web API server for frontend integration
flask>=2.3.0
flask-cors>=4.0.0

# System monitoring for admin panel
psutil>=5.9.0

# Development and testing dependencies (optional)
# pytest>=7.0.0
# black>=22.0.0
# flake8>=4.0.0

# Note: This project is designed to use minimal dependencies
# Most functionality relies on Python's standard library:
# - pathlib (file operations)
# - difflib (diff generation)
# - datetime (timestamps)
# - os (environment variables)
# - time (interval checking)
