/**
 * API utility for handling environment-specific API calls
 */



// Determine the API base URL based on the environment
const getApiBaseUrl = (): string => {
  // In development mode (Vite dev server), the proxy handles the routing
  if (import.meta.env.DEV) {
    return ''
  }
  
  // In production mode, we need to specify the full backend URL
  // You can customize this URL based on your production setup
  return import.meta.env.VITE_API_URL || 'http://localhost:8001'
}

const API_BASE_URL = getApiBaseUrl()

/**
 * Wrapper around fetch that handles the API base URL
 */
export const apiFetch = async (endpoint: string, options?: RequestInit): Promise<Response> => {
  // Ensure the endpoint starts with /api
  if (!endpoint.startsWith('/api')) {
    throw new Error('API endpoints must start with /api')
  }
  
  const url = API_BASE_URL + endpoint
  return fetch(url, options)
}

/**
 * Helper function for JSON API calls
 */
export const apiRequest = async <T = any>(
  endpoint: string, 
  options?: RequestInit
): Promise<T> => {
  const response = await apiFetch(endpoint, options)
  
  if (!response.ok) {
    const error = await response.json().catch(() => ({ error: 'Unknown error' }))
    throw new Error(error.error || `HTTP error! status: ${response.status}`)
  }
  
  return response.json()
}

/**
 * Manual summary generation API response type
 */
export interface ManualSummaryGenerationResponse {
  success: boolean
  message: string
  result?: {
    processed: boolean
    changes_count: number
    processing_time: number
    last_update: string
    reason?: string
    error?: string
  }
}

/**
 * Manually trigger batch AI processing for summary generation
 */
export const triggerManualSummaryGeneration = async (force: boolean = true): Promise<ManualSummaryGenerationResponse> => {
  return apiRequest<ManualSummaryGenerationResponse>('/api/monitor/batch-ai/trigger', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ force })
  })
}

/**
 * Living Note update API response type
 */
export interface LivingNoteUpdateResponse {
  success: boolean
  message: string
  updated: boolean
  summary?: string
  individual_summary_created?: boolean
}

/**
 * Trigger Living Note update (for hybrid summary system)
 */
export const triggerLivingNoteUpdate = async (force: boolean = true): Promise<LivingNoteUpdateResponse> => {
  return apiRequest<LivingNoteUpdateResponse>('/api/living-note/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ force })
  })
}

/**
 * Comprehensive summary generation API response type
 */
export interface ComprehensiveSummaryGenerationResponse {
  success: boolean
  message: string
  result?: {
    processed: boolean
    summary_id?: number
    changes_count: number
    files_count: number
    time_range_start: string
    time_range_end: string
    processing_time: number
    time_span: string
    summary_preview?: string
    reason?: string
    error?: string
  }
}

/**
 * Comprehensive summary data type
 */
export interface ComprehensiveSummary {
  id: number
  timestamp: string
  time_range_start: string
  time_range_end: string
  summary_content: string
  key_topics: string[]
  key_keywords: string[]
  overall_impact: 'brief' | 'moderate' | 'significant'
  files_affected_count: number
  changes_count: number
  time_span: string
  created_at: string
}

/**
 * Comprehensive summaries list response type
 */
export interface ComprehensiveSummariesResponse {
  summaries: ComprehensiveSummary[]
  pagination: {
    current_page: number
    page_size: number
    total_count: number
    total_pages: number
    has_next: boolean
    has_previous: boolean
  }
}

/**
 * Generate comprehensive summary covering everything since last summary
 */
export const triggerComprehensiveSummaryGeneration = async (force: boolean = true): Promise<ComprehensiveSummaryGenerationResponse> => {
  return apiRequest<ComprehensiveSummaryGenerationResponse>('/api/monitor/comprehensive-summary/generate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ force })
  })
}

/**
 * Get paginated list of comprehensive summaries
 */
export const getComprehensiveSummaries = async (page: number = 1, pageSize: number = 10): Promise<ComprehensiveSummariesResponse> => {
  return apiRequest<ComprehensiveSummariesResponse>(`/api/monitor/comprehensive-summary/list?page=${page}&page_size=${pageSize}`)
}

/**
 * Get details of a specific comprehensive summary
 */
export const getComprehensiveSummary = async (summaryId: number): Promise<ComprehensiveSummary> => {
  return apiRequest<ComprehensiveSummary>(`/api/monitor/comprehensive-summary/${summaryId}`)
}

/**
 * Delete a comprehensive summary
 */
export const deleteComprehensiveSummary = async (summaryId: number): Promise<{success: boolean, message: string}> => {
  return apiRequest(`/api/monitor/comprehensive-summary/${summaryId}`, {
    method: 'DELETE'
  })
}