<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Obby - Note Monitor | Midnight Blue Theme</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            color: #cbd5e1;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* Glassmorphism Base */
        .glass {
            background: rgba(15, 23, 42, 0.6);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid rgba(6, 182, 212, 0.1);
            border-radius: 16px;
        }

        .glass-strong {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border: 1px solid rgba(6, 182, 212, 0.2);
            border-radius: 12px;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 70px;
            z-index: 1000;
            padding: 0 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: rgba(15, 23, 42, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(6, 182, 212, 0.1);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 24px;
            font-weight: 700;
            color: #06b6d4;
            text-shadow: 0 0 20px rgba(6, 182, 212, 0.3);
        }

        .logo-icon {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #06b6d4, #0891b2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            box-shadow: 0 4px 16px rgba(6, 182, 212, 0.3);
        }

        .header-controls {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: rgba(6, 182, 212, 0.1);
            border: 1px solid rgba(6, 182, 212, 0.2);
            border-radius: 24px;
            font-size: 14px;
            color: #06b6d4;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #06b6d4;
            border-radius: 50%;
            animation: pulse 2s infinite;
            box-shadow: 0 0 8px rgba(6, 182, 212, 0.6);
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
        }

        /* Layout */
        .app-container {
            display: flex;
            margin-top: 70px;
            min-height: calc(100vh - 70px);
        }

        /* Sidebar */
        .sidebar {
            width: 280px;
            background: rgba(15, 23, 42, 0.8);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border-right: 1px solid rgba(6, 182, 212, 0.1);
            padding: 24px;
            position: fixed;
            height: calc(100vh - 70px);
            overflow-y: auto;
        }

        .sidebar h3 {
            color: #06b6d4;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            margin-bottom: 8px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .nav-item:hover {
            background: rgba(6, 182, 212, 0.1);
            border-color: rgba(6, 182, 212, 0.2);
            transform: translateX(4px);
        }

        .nav-item.active {
            background: rgba(6, 182, 212, 0.15);
            border-color: rgba(6, 182, 212, 0.3);
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #06b6d4;
        }

        .watched-folders {
            margin-top: 32px;
        }

        .folder-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            margin-bottom: 6px;
            font-size: 14px;
            color: #94a3b8;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .folder-item:hover {
            background: rgba(6, 182, 212, 0.05);
            color: #cbd5e1;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: 280px;
            padding: 24px;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #f1f5f9;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #f1f5f9, #06b6d4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .page-subtitle {
            color: #94a3b8;
            font-size: 16px;
        }

        /* Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-bottom: 32px;
        }

        .stat-card {
            padding: 24px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #06b6d4, #0891b2);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 32px rgba(6, 182, 212, 0.1);
        }

        .stat-card:hover::before {
            opacity: 1;
        }

        .stat-value {
            font-size: 36px;
            font-weight: 700;
            color: #06b6d4;
            margin-bottom: 8px;
            text-shadow: 0 0 20px rgba(6, 182, 212, 0.3);
        }

        .stat-label {
            color: #94a3b8;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* File Monitor Dashboard */
        .monitor-section {
            margin-bottom: 32px;
        }

        .section-header {
            display: flex;
            align-items: center;
            justify-content: between;
            margin-bottom: 20px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #f1f5f9;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .filter-tabs {
            display: flex;
            gap: 8px;
        }

        .filter-tab {
            padding: 8px 16px;
            border-radius: 24px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(6, 182, 212, 0.2);
            background: rgba(6, 182, 212, 0.05);
            color: #94a3b8;
        }

        .filter-tab.active,
        .filter-tab:hover {
            background: rgba(6, 182, 212, 0.15);
            border-color: rgba(6, 182, 212, 0.3);
            color: #06b6d4;
        }

        /* File Changes List */
        .file-changes {
            padding: 24px;
            max-height: 600px;
            overflow-y: auto;
        }

        .file-change-item {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            padding: 16px;
            margin-bottom: 12px;
            background: rgba(30, 41, 59, 0.6);
            border: 1px solid rgba(6, 182, 212, 0.1);
            border-radius: 12px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .file-change-item:hover {
            background: rgba(30, 41, 59, 0.8);
            border-color: rgba(6, 182, 212, 0.2);
            transform: translateX(4px);
        }

        .change-type {
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            min-width: 80px;
            text-align: center;
        }

        .change-type.modified {
            background: rgba(6, 182, 212, 0.2);
            color: #06b6d4;
            border: 1px solid rgba(6, 182, 212, 0.3);
        }

        .change-type.added {
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
            border: 1px solid rgba(34, 197, 94, 0.3);
        }

        .change-type.deleted {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .file-info {
            flex: 1;
        }

        .file-path {
            font-weight: 600;
            color: #f1f5f9;
            margin-bottom: 4px;
        }

        .file-meta {
            color: #94a3b8;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .change-summary {
            color: #cbd5e1;
            font-size: 14px;
            line-height: 1.5;
        }

        /* Diff Preview */
        .diff-preview {
            margin-top: 12px;
            padding: 16px;
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(6, 182, 212, 0.1);
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            max-height: 200px;
            overflow-y: auto;
        }

        .diff-line {
            padding: 2px 0;
            line-height: 1.4;
        }

        .diff-line.added {
            color: #22c55e;
            background: rgba(34, 197, 94, 0.1);
            padding-left: 8px;
            margin-left: -8px;
        }

        .diff-line.removed {
            color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
            padding-left: 8px;
            margin-left: -8px;
        }

        .diff-line.context {
            color: #94a3b8;
        }

        /* Real-time Indicator */
        .realtime-indicator {
            position: fixed;
            bottom: 24px;
            right: 24px;
            padding: 12px 20px;
            background: rgba(6, 182, 212, 0.15);
            border: 1px solid rgba(6, 182, 212, 0.3);
            border-radius: 24px;
            color: #06b6d4;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            z-index: 100;
        }

        .realtime-pulse {
            width: 8px;
            height: 8px;
            background: #06b6d4;
            border-radius: 50%;
            animation: pulse 1.5s infinite;
        }

        /* Scrollbar Styling */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(15, 23, 42, 0.5);
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(6, 182, 212, 0.3);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(6, 182, 212, 0.5);
        }

        /* Responsive */
        @media (max-width: 1200px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .header {
                padding: 0 16px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="logo">
            <div class="logo-icon">O</div>
            <span>Obby Monitor</span>
        </div>
        <div class="header-controls">
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span>Monitoring Active</span>
            </div>
        </div>
    </header>

    <div class="app-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <nav>
                <h3>Navigation</h3>
                <div class="nav-item active">
                    <div class="nav-icon">📊</div>
                    <span>Dashboard</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">🔍</div>
                    <span>Search</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">📝</div>
                    <span>Living Note</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">📋</div>
                    <span>Diffs</span>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">⚙️</div>
                    <span>Settings</span>
                </div>
            </nav>

            <div class="watched-folders">
                <h3>Watched Folders</h3>
                <div class="folder-item">
                    <span>📁</span>
                    <span>/docs</span>
                </div>
                <div class="folder-item">
                    <span>📁</span>
                    <span>/notes</span>
                </div>
                <div class="folder-item">
                    <span>📁</span>
                    <span>/projects</span>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <div class="page-header">
                <h1 class="page-title">File Monitor Dashboard</h1>
                <p class="page-subtitle">Real-time monitoring and change tracking</p>
            </div>

            <!-- Stats Grid -->
            <div class="dashboard-grid">
                <div class="stat-card glass">
                    <div class="stat-value">247</div>
                    <div class="stat-label">Files Monitored</div>
                </div>
                <div class="stat-card glass">
                    <div class="stat-value">12</div>
                    <div class="stat-label">Changes Today</div>
                </div>
            </div>

            <!-- Recent Changes -->
            <section class="monitor-section">
                <div class="section-header">
                    <h2 class="section-title">
                        <span>📈</span>
                        Recent Changes
                    </h2>
                    <div class="filter-tabs">
                        <div class="filter-tab active">All</div>
                        <div class="filter-tab">Modified</div>
                        <div class="filter-tab">Added</div>
                        <div class="filter-tab">Deleted</div>
                    </div>
                </div>

                <div class="file-changes glass-strong">
                    <div class="file-change-item">
                        <div class="change-type modified">Modified</div>
                        <div class="file-info">
                            <div class="file-path">notes/project-ideas.md</div>
                            <div class="file-meta">2 minutes ago • +15 lines, -3 lines</div>
                            <div class="change-summary">Added new section about machine learning applications</div>
                            <div class="diff-preview">
                                <div class="diff-line context">## Machine Learning Ideas</div>
                                <div class="diff-line added">+ Natural language processing for document analysis</div>
                                <div class="diff-line added">+ Computer vision for automated quality control</div>
                                <div class="diff-line removed">- Old implementation notes</div>
                                <div class="diff-line context">### Implementation Notes</div>
                            </div>
                        </div>
                    </div>

                    <div class="file-change-item">
                        <div class="change-type added">Added</div>
                        <div class="file-info">
                            <div class="file-path">docs/api-specification.md</div>
                            <div class="file-meta">7 minutes ago • +45 lines</div>
                            <div class="change-summary">Created comprehensive API documentation</div>
                            <div class="diff-preview">
                                <div class="diff-line added">+ # API Specification</div>
                                <div class="diff-line added">+ </div>
                                <div class="diff-line added">+ ## Authentication</div>
                                <div class="diff-line added">+ All API requests require authentication...</div>
                            </div>
                        </div>
                    </div>

                    <div class="file-change-item">
                        <div class="change-type modified">Modified</div>
                        <div class="file-info">
                            <div class="file-path">projects/web-app/README.md</div>
                            <div class="file-meta">15 minutes ago • +8 lines, -2 lines</div>
                            <div class="change-summary">Updated installation instructions and dependencies</div>
                            <div class="diff-preview">
                                <div class="diff-line context">## Installation</div>
                                <div class="diff-line removed">- npm install</div>
                                <div class="diff-line added">+ npm install --legacy-peer-deps</div>
                                <div class="diff-line added">+ npm run build</div>
                                <div class="diff-line context">## Usage</div>
                            </div>
                        </div>
                    </div>

                    <div class="file-change-item">
                        <div class="change-type deleted">Deleted</div>
                        <div class="file-info">
                            <div class="file-path">temp/old-backup.txt</div>
                            <div class="file-meta">32 minutes ago • -127 lines</div>
                            <div class="change-summary">Removed obsolete backup file</div>
                        </div>
                    </div>

                    <div class="file-change-item">
                        <div class="change-type modified">Modified</div>
                        <div class="file-info">
                            <div class="file-path">notes/meeting-notes.md</div>
                            <div class="file-meta">1 hour ago • +23 lines, -1 line</div>
                            <div class="change-summary">Added action items from team meeting</div>
                            <div class="diff-preview">
                                <div class="diff-line context">## Action Items</div>
                                <div class="diff-line added">+ [ ] Review pull request #42</div>
                                <div class="diff-line added">+ [ ] Update deployment documentation</div>
                                <div class="diff-line added">+ [ ] Schedule next architecture review</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Real-time Indicator -->
    <div class="realtime-indicator">
        <div class="realtime-pulse"></div>
        <span>Live monitoring</span>
    </div>

    <script>
        // Simple demo interactions
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.nav-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });

        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Simulate real-time updates
        setInterval(() => {
            const indicator = document.querySelector('.realtime-indicator');
            indicator.style.opacity = '0.7';
            setTimeout(() => {
                indicator.style.opacity = '1';
            }, 200);
        }, 3000);
    </script>
</body>
</html>