<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OBBY - Cyberpunk Note Monitor</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap');
        
        :root {
            --bg-primary: #0a0a0a;
            --bg-secondary: #111111;
            --bg-tertiary: #1a1a1a;
            --neon-pink: #ff00ff;
            --neon-cyan: #00ffff;
            --neon-green: #00ff41;
            --neon-orange: #ff9500;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --text-muted: #666666;
            --border-glow: rgba(255, 0, 255, 0.3);
            --shadow-pink: 0 0 20px rgba(255, 0, 255, 0.5);
            --shadow-cyan: 0 0 20px rgba(0, 255, 255, 0.5);
            --shadow-green: 0 0 20px rgba(0, 255, 65, 0.5);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Rajdhani', monospace;
            background: var(--bg-primary);
            color: var(--text-primary);
            overflow-x: hidden;
            min-height: 100vh;
            background-image: 
                radial-gradient(circle at 20% 20%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(0, 255, 65, 0.05) 0%, transparent 50%);
        }

        /* Neon glow animations */
        @keyframes neonGlow {
            0%, 100% { 
                text-shadow: 
                    0 0 5px currentColor,
                    0 0 10px currentColor,
                    0 0 15px currentColor,
                    0 0 20px currentColor;
            }
            50% { 
                text-shadow: 
                    0 0 2px currentColor,
                    0 0 5px currentColor,
                    0 0 8px currentColor,
                    0 0 12px currentColor;
            }
        }

        @keyframes borderPulse {
            0%, 100% { 
                border-color: var(--neon-pink);
                box-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
            }
            50% { 
                border-color: var(--neon-cyan);
                box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
            }
        }

        @keyframes dataStream {
            0% { transform: translateY(0); opacity: 1; }
            100% { transform: translateY(-100px); opacity: 0; }
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
            border-bottom: 2px solid var(--neon-pink);
            box-shadow: 0 2px 20px rgba(255, 0, 255, 0.3);
            padding: 1rem 2rem;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 0, 255, 0.1), transparent);
            animation: scan 3s infinite;
        }

        @keyframes scan {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .logo {
            font-family: 'Orbitron', monospace;
            font-size: 2rem;
            font-weight: 900;
            color: var(--neon-pink);
            animation: neonGlow 2s ease-in-out infinite alternate;
            text-transform: uppercase;
            letter-spacing: 3px;
        }

        .status-bar {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--neon-green);
            box-shadow: var(--shadow-green);
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
        }

        /* Main Layout */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
            display: grid;
            grid-template-columns: 300px 1fr 350px;
            gap: 2rem;
            min-height: calc(100vh - 120px);
        }

        /* Sidebar */
        .sidebar {
            background: var(--bg-secondary);
            border: 1px solid var(--neon-cyan);
            border-radius: 8px;
            box-shadow: var(--shadow-cyan);
            overflow: hidden;
        }

        .sidebar-header {
            background: linear-gradient(135deg, var(--neon-cyan), var(--neon-pink));
            color: var(--bg-primary);
            padding: 1rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-align: center;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            border-bottom: 1px solid rgba(255, 0, 255, 0.2);
        }

        .nav-link {
            display: block;
            padding: 1rem 1.5rem;
            color: var(--text-secondary);
            text-decoration: none;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 500;
        }

        .nav-link:hover,
        .nav-link.active {
            background: rgba(255, 0, 255, 0.1);
            color: var(--neon-pink);
            text-shadow: 0 0 10px var(--neon-pink);
            transform: translateX(5px);
        }

        /* Main Content */
        .main-content {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .section {
            background: var(--bg-secondary);
            border: 1px solid var(--border-glow);
            border-radius: 8px;
            overflow: hidden;
            animation: borderPulse 4s infinite;
        }

        .section-header {
            background: linear-gradient(135deg, var(--bg-tertiary), var(--bg-secondary));
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--neon-pink);
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .section-title {
            font-family: 'Orbitron', monospace;
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--neon-cyan);
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .section-content {
            padding: 1.5rem;
        }

        /* File Monitor */
        .file-list {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            max-height: 400px;
            overflow-y: auto;
        }

        .file-item {
            background: var(--bg-tertiary);
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 4px;
            padding: 1rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .file-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .file-item:hover::before {
            left: 100%;
        }

        .file-item:hover {
            border-color: var(--neon-cyan);
            box-shadow: var(--shadow-cyan);
            transform: translateY(-2px);
        }

        .file-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .file-name {
            font-family: 'Orbitron', monospace;
            color: var(--neon-green);
            font-weight: 600;
            text-shadow: 0 0 5px var(--neon-green);
        }

        .file-status {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .status-modified {
            background: rgba(255, 149, 0, 0.2);
            color: var(--neon-orange);
            border: 1px solid var(--neon-orange);
        }

        .status-new {
            background: rgba(0, 255, 65, 0.2);
            color: var(--neon-green);
            border: 1px solid var(--neon-green);
        }

        .status-deleted {
            background: rgba(255, 0, 255, 0.2);
            color: var(--neon-pink);
            border: 1px solid var(--neon-pink);
        }

        .file-meta {
            display: flex;
            gap: 1rem;
            font-size: 0.85rem;
            color: var(--text-muted);
            font-family: 'Orbitron', monospace;
        }

        /* Activity Panel */
        .activity-panel {
            background: var(--bg-secondary);
            border: 1px solid var(--neon-pink);
            border-radius: 8px;
            box-shadow: var(--shadow-pink);
        }

        .activity-header {
            background: linear-gradient(135deg, var(--neon-pink), var(--neon-orange));
            color: var(--bg-primary);
            padding: 1rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-align: center;
        }

        .activity-feed {
            padding: 1rem;
            max-height: 500px;
            overflow-y: auto;
        }

        .activity-item {
            padding: 0.75rem;
            margin-bottom: 0.75rem;
            background: var(--bg-tertiary);
            border-left: 3px solid var(--neon-cyan);
            border-radius: 0 4px 4px 0;
            position: relative;
            animation: slideIn 0.5s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateX(20px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .activity-time {
            font-family: 'Orbitron', monospace;
            font-size: 0.75rem;
            color: var(--neon-cyan);
            margin-bottom: 0.25rem;
        }

        .activity-text {
            color: var(--text-secondary);
            line-height: 1.4;
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .stat-card {
            background: var(--bg-tertiary);
            border: 1px solid var(--neon-green);
            border-radius: 6px;
            padding: 1.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--neon-pink), var(--neon-cyan), var(--neon-green));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .stat-card:hover::before {
            transform: scaleX(1);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-green);
        }

        .stat-value {
            font-family: 'Orbitron', monospace;
            font-size: 2rem;
            font-weight: 900;
            color: var(--neon-green);
            text-shadow: 0 0 10px var(--neon-green);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            text-transform: uppercase;
            letter-spacing: 1px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* Search Bar */
        .search-container {
            position: relative;
            margin-bottom: 1.5rem;
        }

        .search-input {
            width: 100%;
            padding: 1rem 1.5rem;
            background: var(--bg-tertiary);
            border: 2px solid var(--neon-cyan);
            border-radius: 25px;
            color: var(--text-primary);
            font-family: 'Orbitron', monospace;
            font-size: 1rem;
            outline: none;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            border-color: var(--neon-pink);
            box-shadow: 0 0 20px rgba(255, 0, 255, 0.3);
            text-shadow: 0 0 5px var(--text-primary);
        }

        .search-input::placeholder {
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-primary);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, var(--neon-pink), var(--neon-cyan));
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(180deg, var(--neon-cyan), var(--neon-pink));
        }

        /* Floating data streams */
        .data-stream {
            position: fixed;
            right: -50px;
            font-family: 'Orbitron', monospace;
            font-size: 0.8rem;
            color: var(--neon-green);
            opacity: 0.3;
            z-index: -1;
            animation: dataStream 15s linear infinite;
        }

        /* Responsive */
        @media (max-width: 1200px) {
            .container {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .sidebar,
            .activity-panel {
                order: 2;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .header {
                padding: 1rem;
            }
            
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            
            .status-bar {
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Floating data streams -->
    <div class="data-stream" style="top: 10%; animation-delay: 0s;">010101110101</div>
    <div class="data-stream" style="top: 30%; animation-delay: 2s;">11010010101</div>
    <div class="data-stream" style="top: 50%; animation-delay: 4s;">001101110110</div>
    <div class="data-stream" style="top: 70%; animation-delay: 6s;">101010011101</div>
    <div class="data-stream" style="top: 90%; animation-delay: 8s;">110100110101</div>

    <header class="header">
        <div class="header-content">
            <div class="logo">OBBY.EXE</div>
            <div class="status-bar">
                <div class="status-item">
                    <div class="status-dot"></div>
                    <span>System Online</span>
                </div>
                <div class="status-item">
                    <div class="status-dot" style="background: var(--neon-cyan); box-shadow: var(--shadow-cyan);"></div>
                    <span>Monitoring Active</span>
                </div>
                <div class="status-item">
                    <div class="status-dot" style="background: var(--neon-orange); box-shadow: 0 0 20px rgba(255, 149, 0, 0.5);"></div>
                    <span>AI Processing</span>
                </div>
            </div>
        </div>
    </header>

    <div class="container">
        <aside class="sidebar">
            <div class="sidebar-header">Navigation</div>
            <nav>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#" class="nav-link active">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">File Monitor</a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">Search</a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">Living Notes</a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">Analytics</a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">Settings</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <main class="main-content">
            <div class="search-container">
                <input type="text" class="search-input" placeholder="Search files, content, topics...">
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">1,247</div>
                    <div class="stat-label">Files Monitored</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">342</div>
                    <div class="stat-label">Changes Today</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">89</div>
                    <div class="stat-label">AI Summaries</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">15.2GB</div>
                    <div class="stat-label">Data Processed</div>
                </div>
            </div>

            <div class="section">
                <div class="section-header">
                    <h2 class="section-title">Recent File Changes</h2>
                </div>
                <div class="section-content">
                    <div class="file-list">
                        <div class="file-item">
                            <div class="file-header">
                                <div class="file-name">neural_network.py</div>
                                <div class="file-status status-modified">Modified</div>
                            </div>
                            <div class="file-meta">
                                <span>Size: 15.7KB</span>
                                <span>Modified: 2m ago</span>
                                <span>Lines: +47 -12</span>
                            </div>
                        </div>
                        
                        <div class="file-item">
                            <div class="file-header">
                                <div class="file-name">cyberpunk_theme.css</div>
                                <div class="file-status status-new">New</div>
                            </div>
                            <div class="file-meta">
                                <span>Size: 8.3KB</span>
                                <span>Created: 5m ago</span>
                                <span>Lines: +234</span>
                            </div>
                        </div>
                        
                        <div class="file-item">
                            <div class="file-header">
                                <div class="file-name">deprecated_module.js</div>
                                <div class="file-status status-deleted">Deleted</div>
                            </div>
                            <div class="file-meta">
                                <span>Size: 4.1KB</span>
                                <span>Deleted: 8m ago</span>
                                <span>Lines: -156</span>
                            </div>
                        </div>
                        
                        <div class="file-item">
                            <div class="file-header">
                                <div class="file-name">api_endpoints.ts</div>
                                <div class="file-status status-modified">Modified</div>
                            </div>
                            <div class="file-meta">
                                <span>Size: 22.4KB</span>
                                <span>Modified: 12m ago</span>
                                <span>Lines: +28 -5</span>
                            </div>
                        </div>
                        
                        <div class="file-item">
                            <div class="file-header">
                                <div class="file-name">quantum_processor.py</div>
                                <div class="file-status status-new">New</div>
                            </div>
                            <div class="file-meta">
                                <span>Size: 31.2KB</span>
                                <span>Created: 15m ago</span>
                                <span>Lines: +892</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <div class="section-header">
                    <h2 class="section-title">AI Analysis Summary</h2>
                </div>
                <div class="section-content">
                    <p style="color: var(--text-secondary); line-height: 1.6; margin-bottom: 1rem;">
                        Recent analysis detected significant changes in neural network architecture. 
                        New quantum processing module introduces advanced computational capabilities.
                        Performance optimizations detected in API layer with reduced latency patterns.
                    </p>
                    <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                        <span style="background: rgba(255, 0, 255, 0.2); color: var(--neon-pink); padding: 0.5rem 1rem; border-radius: 15px; font-size: 0.85rem; border: 1px solid var(--neon-pink);">
                            #machine-learning
                        </span>
                        <span style="background: rgba(0, 255, 255, 0.2); color: var(--neon-cyan); padding: 0.5rem 1rem; border-radius: 15px; font-size: 0.85rem; border: 1px solid var(--neon-cyan);">
                            #quantum-computing
                        </span>
                        <span style="background: rgba(0, 255, 65, 0.2); color: var(--neon-green); padding: 0.5rem 1rem; border-radius: 15px; font-size: 0.85rem; border: 1px solid var(--neon-green);">
                            #performance
                        </span>
                    </div>
                </div>
            </div>
        </main>

        <aside class="activity-panel">
            <div class="activity-header">System Activity</div>
            <div class="activity-feed">
                <div class="activity-item">
                    <div class="activity-time">23:47:32</div>
                    <div class="activity-text">Neural network training initiated</div>
                </div>
                <div class="activity-item">
                    <div class="activity-time">23:45:18</div>
                    <div class="activity-text">File sync completed: 1,247 files processed</div>
                </div>
                <div class="activity-item">
                    <div class="activity-time">23:42:56</div>
                    <div class="activity-text">AI analysis generated for quantum_processor.py</div>
                </div>
                <div class="activity-item">
                    <div class="activity-time">23:40:23</div>
                    <div class="activity-text">Database optimization completed</div>
                </div>
                <div class="activity-item">
                    <div class="activity-time">23:38:44</div>
                    <div class="activity-text">Security scan: No threats detected</div>
                </div>
                <div class="activity-item">
                    <div class="activity-time">23:35:12</div>
                    <div class="activity-text">Memory usage optimized: 2.3GB freed</div>
                </div>
                <div class="activity-item">
                    <div class="activity-time">23:33:07</div>
                    <div class="activity-text">Backup process completed successfully</div>
                </div>
                <div class="activity-item">
                    <div class="activity-time">23:30:45</div>
                    <div class="activity-text">System monitoring protocols activated</div>
                </div>
            </div>
        </aside>
    </div>

    <script>
        // Add some interactive cyberpunk effects
        document.addEventListener('DOMContentLoaded', function() {
            // Simulate real-time updates
            const activityFeed = document.querySelector('.activity-feed');
            const activities = [
                'Quantum matrix calculations processed',
                'Neural pathway optimization complete',
                'Blockchain verification successful',
                'Holographic data stream analyzed',
                'Cybernetic interface synchronized',
                'Digital consciousness backup complete',
                'Virtual reality rendering optimized',
                'Artificial intelligence learning cycle finished'
            ];

            setInterval(() => {
                const now = new Date();
                const timeStr = now.toTimeString().slice(0, 8);
                const randomActivity = activities[Math.floor(Math.random() * activities.length)];
                
                const newActivity = document.createElement('div');
                newActivity.className = 'activity-item';
                newActivity.innerHTML = `
                    <div class="activity-time">${timeStr}</div>
                    <div class="activity-text">${randomActivity}</div>
                `;
                
                activityFeed.insertBefore(newActivity, activityFeed.firstChild);
                
                // Remove oldest activity to keep list manageable
                if (activityFeed.children.length > 10) {
                    activityFeed.removeChild(activityFeed.lastChild);
                }
            }, 5000);

            // Add glitch effect to logo
            const logo = document.querySelector('.logo');
            setInterval(() => {
                logo.style.textShadow = `
                    2px 0 var(--neon-pink),
                    -2px 0 var(--neon-cyan),
                    0 0 10px var(--neon-pink),
                    0 0 20px var(--neon-cyan)
                `;
                setTimeout(() => {
                    logo.style.textShadow = '';
                }, 100);
            }, 3000);

            // Simulate file changes
            const fileItems = document.querySelectorAll('.file-item');
            setInterval(() => {
                const randomFile = fileItems[Math.floor(Math.random() * fileItems.length)];
                randomFile.style.borderColor = 'var(--neon-pink)';
                randomFile.style.boxShadow = 'var(--shadow-pink)';
                setTimeout(() => {
                    randomFile.style.borderColor = '';
                    randomFile.style.boxShadow = '';
                }, 1000);
            }, 2000);
        });
    </script>
</body>
</html>