{"name": "obby-frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"start": "vite", "dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx,ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "lucide-react": "^0.263.1", "date-fns": "^2.30.0", "react-router-dom": "^6.14.1", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "react-syntax-highlighter": "^15.5.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-syntax-highlighter": "^15.5.11", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.27", "@tailwindcss/typography": "^0.5.10", "tailwindcss": "^3.3.3", "typescript": "^5.0.2", "vite": "^4.4.5"}}